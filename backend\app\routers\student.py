from fastapi import APIRouter, Depends, HTTPException, status, Query, UploadFile, File
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, and_, or_, desc
from typing import List, Optional
from datetime import datetime, timedelta
import logging
import os
import uuid

from ..models.user import User, UserRole
from ..models.lab import Lab, LabAssignment, AssignmentSubmission, AssignmentProgressLog, StudentLabAccess
from ..models.virtual_machine import VirtualMachine
from ..database import get_db
from ..routers.auth import get_current_user
from ..schemas.student import (
    AssignmentCreate, AssignmentUpdate, AssignmentResponse,
    SubmissionCreate, SubmissionUpdate, SubmissionResponse,
    ProgressLogCreate, ProgressLogResponse,
    StudentLabAccessCreate, StudentLabAccessUpdate, StudentLabAccessResponse,
    StudentDashboardResponse, StudentDashboardStats, StudentAssignmentSummary,
    StudentLabSummary, GradeSubmissionRequest, BulkGradeRequest, GradingStatsResponse
)

router = APIRouter(prefix="/student", tags=["student"])
logger = logging.getLogger(__name__)

def require_student_or_instructor(current_user: User = Depends(get_current_user)) -> User:
    """Dependency to ensure only students or instructors can access student endpoints."""
    if current_user.role not in [UserRole.STUDENT, UserRole.TEACHER, UserRole.ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Student, instructor, or admin access required"
        )
    return current_user

def require_instructor_or_admin(current_user: User = Depends(get_current_user)) -> User:
    """Dependency to ensure only instructors or admins can manage assignments."""
    if current_user.role not in [UserRole.TEACHER, UserRole.ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Instructor or admin access required"
        )
    return current_user

# Student Dashboard Endpoints

@router.get("/dashboard", response_model=StudentDashboardResponse)
async def get_student_dashboard(
    current_user: User = Depends(require_student_or_instructor),
    db: Session = Depends(get_db)
):
    """Get student dashboard with assignments, labs, and activity."""
    logger.info(f"User {current_user.username} requesting dashboard")
    
    # For instructors/admins, they might be viewing as a student for testing
    student_id = current_user.id
    
    # Get assignments for this student
    assignments_query = db.query(LabAssignment).options(
        joinedload(LabAssignment.lab),
        joinedload(LabAssignment.virtual_machine)
    ).filter(LabAssignment.student_id == student_id)
    
    assignments = assignments_query.all()
    
    # Calculate stats
    total_assignments = len(assignments)
    completed_assignments = len([a for a in assignments if a.is_completed])
    in_progress_assignments = len([a for a in assignments if a.status == 'in_progress'])
    
    # Check for overdue assignments
    now = datetime.utcnow()
    overdue_assignments = len([a for a in assignments if a.due_date and a.due_date < now and not a.is_completed])
    
    # Get labs the student has access to
    lab_access_query = db.query(StudentLabAccess).options(
        joinedload(StudentLabAccess.lab)
    ).filter(
        StudentLabAccess.student_id == student_id,
        StudentLabAccess.is_active == True
    )
    lab_accesses = lab_access_query.all()
    
    total_labs = len(lab_accesses)
    active_labs = len([la for la in lab_accesses if not la.access_expires_date or la.access_expires_date > now])
    
    # Get VMs owned by student
    vms = db.query(VirtualMachine).filter(VirtualMachine.owner_id == student_id).all()
    total_vms = len(vms)
    running_vms = len([vm for vm in vms if vm.status == 'running'])
    
    # Calculate average score
    graded_assignments = [a for a in assignments if a.score is not None]
    average_score = sum(a.score for a in graded_assignments) / len(graded_assignments) if graded_assignments else None
    
    # Calculate total time spent
    total_time_spent_minutes = sum(a.time_spent_minutes for a in assignments)
    total_time_spent_hours = total_time_spent_minutes / 60.0
    
    # Build stats
    stats = StudentDashboardStats(
        total_assignments=total_assignments,
        completed_assignments=completed_assignments,
        in_progress_assignments=in_progress_assignments,
        overdue_assignments=overdue_assignments,
        total_labs=total_labs,
        active_labs=active_labs,
        total_vms=total_vms,
        running_vms=running_vms,
        average_score=average_score,
        total_time_spent_hours=total_time_spent_hours
    )
    
    # Get recent assignments (last 10)
    recent_assignments = []
    for assignment in sorted(assignments, key=lambda x: x.updated_at, reverse=True)[:10]:
        is_overdue = assignment.due_date and assignment.due_date < now and not assignment.is_completed
        recent_assignments.append(StudentAssignmentSummary(
            id=assignment.id,
            title=assignment.title,
            lab_name=assignment.lab.name if assignment.lab else "Unknown Lab",
            status=assignment.status,
            progress_percentage=assignment.progress_percentage,
            due_date=assignment.due_date,
            score=assignment.score,
            max_score=assignment.max_score,
            difficulty_level=assignment.difficulty_level,
            estimated_duration_minutes=assignment.estimated_duration_minutes,
            time_spent_minutes=assignment.time_spent_minutes,
            is_overdue=is_overdue
        ))
    
    # Get active labs
    active_lab_summaries = []
    for lab_access in lab_accesses:
        if not lab_access.access_expires_date or lab_access.access_expires_date > now:
            lab = lab_access.lab
            lab_assignments = [a for a in assignments if a.lab_id == lab.id]
            completed_lab_assignments = [a for a in lab_assignments if a.is_completed]
            
            # Get VMs in this lab
            lab_vms = db.query(VirtualMachine).join(VirtualMachine.labs).filter(
                Lab.id == lab.id,
                VirtualMachine.owner_id == student_id
            ).count()
            
            active_lab_summaries.append(StudentLabSummary(
                id=lab.id,
                name=lab.name,
                description=lab.description,
                instructor_name=lab.instructor.username if lab.instructor else None,
                assignment_count=len(lab_assignments),
                completed_assignments=len(completed_lab_assignments),
                vm_count=lab_vms,
                access_expires_date=lab_access.access_expires_date,
                last_access_date=lab_access.last_access_date,
                total_time_spent_minutes=lab_access.total_time_spent_minutes
            ))
    
    # Get recent activity (last 20 progress logs)
    recent_activity_query = db.query(AssignmentProgressLog).options(
        joinedload(AssignmentProgressLog.assignment),
        joinedload(AssignmentProgressLog.virtual_machine)
    ).filter(
        AssignmentProgressLog.student_id == student_id
    ).order_by(desc(AssignmentProgressLog.created_at)).limit(20)
    
    recent_activity = []
    for log in recent_activity_query:
        recent_activity.append(ProgressLogResponse(
            id=log.id,
            assignment_id=log.assignment_id,
            student_id=log.student_id,
            action=log.action,
            description=log.description,
            progress_percentage=log.progress_percentage,
            vm_id=log.vm_id,
            step_number=log.step_number,
            context_data=log.context_data,
            session_duration_minutes=log.session_duration_minutes,
            created_at=log.created_at,
            assignment_title=log.assignment.title if log.assignment else None,
            student_name=current_user.username,
            vm_name=log.virtual_machine.name if log.virtual_machine else None
        ))
    
    return StudentDashboardResponse(
        stats=stats,
        recent_assignments=recent_assignments,
        active_labs=active_lab_summaries,
        recent_activity=recent_activity
    )

@router.get("/assignments", response_model=List[AssignmentResponse])
async def get_student_assignments(
    status: Optional[str] = Query(None),
    lab_id: Optional[int] = Query(None),
    current_user: User = Depends(require_student_or_instructor),
    db: Session = Depends(get_db)
):
    """Get assignments for the current student."""
    logger.info(f"User {current_user.username} requesting assignments")
    
    query = db.query(LabAssignment).options(
        joinedload(LabAssignment.lab),
        joinedload(LabAssignment.student),
        joinedload(LabAssignment.virtual_machine)
    ).filter(LabAssignment.student_id == current_user.id)
    
    if status:
        query = query.filter(LabAssignment.status == status)
    
    if lab_id:
        query = query.filter(LabAssignment.lab_id == lab_id)
    
    assignments = query.order_by(desc(LabAssignment.updated_at)).all()
    
    assignment_responses = []
    for assignment in assignments:
        assignment_responses.append(AssignmentResponse(
            id=assignment.id,
            title=assignment.title,
            description=assignment.description,
            instructions=assignment.instructions,
            assignment_type=assignment.assignment_type,
            difficulty_level=assignment.difficulty_level,
            estimated_duration_minutes=assignment.estimated_duration_minutes,
            required_templates=assignment.required_templates,
            required_vms=assignment.required_vms,
            max_score=assignment.max_score,
            auto_grade=assignment.auto_grade,
            due_date=assignment.due_date,
            lab_id=assignment.lab_id,
            student_id=assignment.student_id,
            vm_id=assignment.vm_id,
            status=assignment.status,
            is_active=assignment.is_active,
            is_completed=assignment.is_completed,
            completion_date=assignment.completion_date,
            progress_percentage=assignment.progress_percentage,
            notes=assignment.notes,
            score=assignment.score,
            feedback=assignment.feedback,
            time_spent_minutes=assignment.time_spent_minutes,
            attempts_count=assignment.attempts_count,
            last_activity=assignment.last_activity,
            submitted_date=assignment.submitted_date,
            graded_date=assignment.graded_date,
            created_at=assignment.created_at,
            updated_at=assignment.updated_at,
            lab_name=assignment.lab.name if assignment.lab else None,
            student_name=assignment.student.username if assignment.student else None,
            vm_name=assignment.virtual_machine.name if assignment.virtual_machine else None
        ))
    
    return assignment_responses

@router.get("/assignments/{assignment_id}", response_model=AssignmentResponse)
async def get_assignment_details(
    assignment_id: int,
    current_user: User = Depends(require_student_or_instructor),
    db: Session = Depends(get_db)
):
    """Get detailed assignment information."""
    assignment = db.query(LabAssignment).options(
        joinedload(LabAssignment.lab),
        joinedload(LabAssignment.student),
        joinedload(LabAssignment.virtual_machine)
    ).filter(
        LabAssignment.id == assignment_id,
        LabAssignment.student_id == current_user.id
    ).first()
    
    if not assignment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Assignment not found"
        )
    
    return AssignmentResponse(
        id=assignment.id,
        title=assignment.title,
        description=assignment.description,
        instructions=assignment.instructions,
        assignment_type=assignment.assignment_type,
        difficulty_level=assignment.difficulty_level,
        estimated_duration_minutes=assignment.estimated_duration_minutes,
        required_templates=assignment.required_templates,
        required_vms=assignment.required_vms,
        max_score=assignment.max_score,
        auto_grade=assignment.auto_grade,
        due_date=assignment.due_date,
        lab_id=assignment.lab_id,
        student_id=assignment.student_id,
        vm_id=assignment.vm_id,
        status=assignment.status,
        is_active=assignment.is_active,
        is_completed=assignment.is_completed,
        completion_date=assignment.completion_date,
        progress_percentage=assignment.progress_percentage,
        notes=assignment.notes,
        score=assignment.score,
        feedback=assignment.feedback,
        time_spent_minutes=assignment.time_spent_minutes,
        attempts_count=assignment.attempts_count,
        last_activity=assignment.last_activity,
        submitted_date=assignment.submitted_date,
        graded_date=assignment.graded_date,
        created_at=assignment.created_at,
        updated_at=assignment.updated_at,
        lab_name=assignment.lab.name if assignment.lab else None,
        student_name=assignment.student.username if assignment.student else None,
        vm_name=assignment.virtual_machine.name if assignment.virtual_machine else None
    )

@router.post("/assignments/{assignment_id}/start")
async def start_assignment(
    assignment_id: int,
    current_user: User = Depends(require_student_or_instructor),
    db: Session = Depends(get_db)
):
    """Start working on an assignment."""
    assignment = db.query(LabAssignment).filter(
        LabAssignment.id == assignment_id,
        LabAssignment.student_id == current_user.id
    ).first()

    if not assignment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Assignment not found"
        )

    if assignment.status == 'assigned':
        assignment.status = 'in_progress'
        assignment.last_activity = datetime.utcnow()
        assignment.attempts_count += 1

        # Log progress
        progress_log = AssignmentProgressLog(
            assignment_id=assignment_id,
            student_id=current_user.id,
            action='started',
            description='Student started working on assignment',
            progress_percentage=0
        )
        db.add(progress_log)

        db.commit()

        logger.info(f"Student {current_user.username} started assignment {assignment_id}")

    return {"message": "Assignment started successfully"}

@router.post("/assignments/{assignment_id}/progress")
async def update_assignment_progress(
    assignment_id: int,
    progress_data: ProgressLogCreate,
    current_user: User = Depends(require_student_or_instructor),
    db: Session = Depends(get_db)
):
    """Update assignment progress."""
    assignment = db.query(LabAssignment).filter(
        LabAssignment.id == assignment_id,
        LabAssignment.student_id == current_user.id
    ).first()

    if not assignment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Assignment not found"
        )

    try:
        # Update assignment progress
        assignment.progress_percentage = progress_data.progress_percentage
        assignment.last_activity = datetime.utcnow()

        if progress_data.session_duration_minutes:
            assignment.time_spent_minutes += progress_data.session_duration_minutes

        # Create progress log
        progress_log = AssignmentProgressLog(
            assignment_id=assignment_id,
            student_id=current_user.id,
            action=progress_data.action,
            description=progress_data.description,
            progress_percentage=progress_data.progress_percentage,
            vm_id=progress_data.vm_id,
            step_number=progress_data.step_number,
            context_data=progress_data.context_data,
            session_duration_minutes=progress_data.session_duration_minutes
        )
        db.add(progress_log)

        # Check if assignment is completed
        if progress_data.progress_percentage >= 100 and not assignment.is_completed:
            assignment.is_completed = True
            assignment.completion_date = datetime.utcnow()
            assignment.status = 'completed'

            # Auto-grade if enabled
            if assignment.auto_grade:
                assignment.score = assignment.max_score
                assignment.graded_date = datetime.utcnow()
                assignment.status = 'graded'

        db.commit()

        logger.info(f"Progress updated for assignment {assignment_id}: {progress_data.progress_percentage}%")

        return {"message": "Progress updated successfully", "progress_percentage": assignment.progress_percentage}

    except Exception as e:
        logger.error(f"Failed to update progress for assignment {assignment_id}: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update progress"
        )

@router.post("/assignments/{assignment_id}/submit")
async def submit_assignment(
    assignment_id: int,
    submission_data: SubmissionCreate,
    current_user: User = Depends(require_student_or_instructor),
    db: Session = Depends(get_db)
):
    """Submit an assignment."""
    assignment = db.query(LabAssignment).filter(
        LabAssignment.id == assignment_id,
        LabAssignment.student_id == current_user.id
    ).first()

    if not assignment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Assignment not found"
        )

    try:
        # Get current submission version
        last_submission = db.query(AssignmentSubmission).filter(
            AssignmentSubmission.assignment_id == assignment_id,
            AssignmentSubmission.student_id == current_user.id
        ).order_by(desc(AssignmentSubmission.version)).first()

        next_version = (last_submission.version + 1) if last_submission else 1

        # Create submission
        submission = AssignmentSubmission(
            assignment_id=assignment_id,
            student_id=current_user.id,
            submission_type=submission_data.submission_type,
            content=submission_data.content,
            submission_notes=submission_data.submission_notes,
            is_final=submission_data.is_final,
            version=next_version
        )
        db.add(submission)

        # Update assignment status
        assignment.status = 'submitted'
        assignment.submitted_date = datetime.utcnow()
        assignment.last_activity = datetime.utcnow()

        if submission_data.is_final:
            assignment.is_completed = True
            assignment.completion_date = datetime.utcnow()

        # Log progress
        progress_log = AssignmentProgressLog(
            assignment_id=assignment_id,
            student_id=current_user.id,
            action='submitted',
            description=f'Assignment submitted (version {next_version})',
            progress_percentage=assignment.progress_percentage
        )
        db.add(progress_log)

        db.commit()
        db.refresh(submission)

        logger.info(f"Assignment {assignment_id} submitted by student {current_user.username}")

        return {"message": "Assignment submitted successfully", "submission_id": submission.id, "version": next_version}

    except Exception as e:
        logger.error(f"Failed to submit assignment {assignment_id}: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to submit assignment"
        )

@router.get("/assignments/{assignment_id}/submissions", response_model=List[SubmissionResponse])
async def get_assignment_submissions(
    assignment_id: int,
    current_user: User = Depends(require_student_or_instructor),
    db: Session = Depends(get_db)
):
    """Get all submissions for an assignment."""
    # Verify assignment access
    assignment = db.query(LabAssignment).filter(
        LabAssignment.id == assignment_id,
        LabAssignment.student_id == current_user.id
    ).first()

    if not assignment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Assignment not found"
        )

    submissions = db.query(AssignmentSubmission).options(
        joinedload(AssignmentSubmission.assignment),
        joinedload(AssignmentSubmission.student),
        joinedload(AssignmentSubmission.graded_by)
    ).filter(
        AssignmentSubmission.assignment_id == assignment_id,
        AssignmentSubmission.student_id == current_user.id
    ).order_by(desc(AssignmentSubmission.version)).all()

    submission_responses = []
    for submission in submissions:
        submission_responses.append(SubmissionResponse(
            id=submission.id,
            assignment_id=submission.assignment_id,
            student_id=submission.student_id,
            submission_type=submission.submission_type,
            content=submission.content,
            file_path=submission.file_path,
            file_name=submission.file_name,
            file_size=submission.file_size,
            vm_snapshot_id=submission.vm_snapshot_id,
            vm_config=submission.vm_config,
            submission_notes=submission.submission_notes,
            is_final=submission.is_final,
            version=submission.version,
            is_graded=submission.is_graded,
            score=submission.score,
            feedback=submission.feedback,
            graded_by_id=submission.graded_by_id,
            graded_date=submission.graded_date,
            submitted_date=submission.submitted_date,
            created_at=submission.created_at,
            updated_at=submission.updated_at,
            assignment_title=submission.assignment.title if submission.assignment else None,
            student_name=submission.student.username if submission.student else None,
            graded_by_name=submission.graded_by.username if submission.graded_by else None
        ))

    return submission_responses

@router.get("/labs", response_model=List[StudentLabAccessResponse])
async def get_student_labs(
    current_user: User = Depends(require_student_or_instructor),
    db: Session = Depends(get_db)
):
    """Get labs accessible to the current student."""
    lab_accesses = db.query(StudentLabAccess).options(
        joinedload(StudentLabAccess.lab),
        joinedload(StudentLabAccess.student)
    ).filter(
        StudentLabAccess.student_id == current_user.id,
        StudentLabAccess.is_active == True
    ).all()

    access_responses = []
    for access in lab_accesses:
        access_responses.append(StudentLabAccessResponse(
            id=access.id,
            student_id=access.student_id,
            lab_id=access.lab_id,
            access_granted_date=access.access_granted_date,
            access_expires_date=access.access_expires_date,
            is_active=access.is_active,
            first_access_date=access.first_access_date,
            last_access_date=access.last_access_date,
            total_access_count=access.total_access_count,
            total_time_spent_minutes=access.total_time_spent_minutes,
            can_create_vms=access.can_create_vms,
            can_delete_vms=access.can_delete_vms,
            max_vms_allowed=access.max_vms_allowed,
            created_at=access.created_at,
            updated_at=access.updated_at,
            student_name=access.student.username if access.student else None,
            lab_name=access.lab.name if access.lab else None
        ))

    return access_responses
