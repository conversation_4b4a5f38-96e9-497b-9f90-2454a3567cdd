from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime

# Assignment Schemas
class AssignmentBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    instructions: Optional[str] = None
    assignment_type: str = Field(default='lab_exercise', pattern='^(lab_exercise|project|quiz|practical)$')
    difficulty_level: str = Field(default='beginner', pattern='^(beginner|intermediate|advanced)$')
    estimated_duration_minutes: int = Field(default=60, ge=1, le=480)
    required_templates: Optional[List[int]] = None
    required_vms: int = Field(default=1, ge=1, le=10)
    max_score: int = Field(default=100, ge=1, le=1000)
    auto_grade: bool = False
    due_date: Optional[datetime] = None

class AssignmentCreate(AssignmentBase):
    lab_id: int
    student_ids: List[int]

class AssignmentUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    instructions: Optional[str] = None
    assignment_type: Optional[str] = Field(None, pattern='^(lab_exercise|project|quiz|practical)$')
    difficulty_level: Optional[str] = Field(None, pattern='^(beginner|intermediate|advanced)$')
    estimated_duration_minutes: Optional[int] = Field(None, ge=1, le=480)
    required_templates: Optional[List[int]] = None
    required_vms: Optional[int] = Field(None, ge=1, le=10)
    max_score: Optional[int] = Field(None, ge=1, le=1000)
    auto_grade: Optional[bool] = None
    due_date: Optional[datetime] = None
    is_active: Optional[bool] = None

class AssignmentResponse(AssignmentBase):
    id: int
    lab_id: int
    student_id: int
    vm_id: Optional[int] = None
    status: str
    is_active: bool
    is_completed: bool
    completion_date: Optional[datetime] = None
    progress_percentage: int
    notes: Optional[str] = None
    score: Optional[int] = None
    feedback: Optional[str] = None
    time_spent_minutes: int
    attempts_count: int
    last_activity: Optional[datetime] = None
    submitted_date: Optional[datetime] = None
    graded_date: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    
    # Related data
    lab_name: Optional[str] = None
    student_name: Optional[str] = None
    vm_name: Optional[str] = None

    class Config:
        from_attributes = True

# Assignment Submission Schemas
class SubmissionBase(BaseModel):
    submission_type: str = Field(default='file', pattern='^(file|text|url|vm_snapshot)$')
    content: Optional[str] = None
    submission_notes: Optional[str] = None
    is_final: bool = False

class SubmissionCreate(SubmissionBase):
    assignment_id: int

class SubmissionUpdate(BaseModel):
    content: Optional[str] = None
    submission_notes: Optional[str] = None
    is_final: Optional[bool] = None

class SubmissionResponse(SubmissionBase):
    id: int
    assignment_id: int
    student_id: int
    file_path: Optional[str] = None
    file_name: Optional[str] = None
    file_size: Optional[int] = None
    vm_snapshot_id: Optional[str] = None
    vm_config: Optional[Dict[str, Any]] = None
    version: int
    is_graded: bool
    score: Optional[int] = None
    feedback: Optional[str] = None
    graded_by_id: Optional[int] = None
    graded_date: Optional[datetime] = None
    submitted_date: datetime
    created_at: datetime
    updated_at: datetime
    
    # Related data
    assignment_title: Optional[str] = None
    student_name: Optional[str] = None
    graded_by_name: Optional[str] = None

    class Config:
        from_attributes = True

# Progress Tracking Schemas
class ProgressLogCreate(BaseModel):
    assignment_id: int
    action: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    progress_percentage: int = Field(default=0, ge=0, le=100)
    vm_id: Optional[int] = None
    step_number: Optional[int] = None
    context_data: Optional[Dict[str, Any]] = None
    session_duration_minutes: Optional[int] = None

class ProgressLogResponse(BaseModel):
    id: int
    assignment_id: int
    student_id: int
    action: str
    description: Optional[str] = None
    progress_percentage: int
    vm_id: Optional[int] = None
    step_number: Optional[int] = None
    context_data: Optional[Dict[str, Any]] = None
    session_duration_minutes: Optional[int] = None
    created_at: datetime
    
    # Related data
    assignment_title: Optional[str] = None
    student_name: Optional[str] = None
    vm_name: Optional[str] = None

    class Config:
        from_attributes = True

# Student Lab Access Schemas
class StudentLabAccessCreate(BaseModel):
    student_id: int
    lab_id: int
    access_expires_date: Optional[datetime] = None
    can_create_vms: bool = True
    can_delete_vms: bool = False
    max_vms_allowed: int = Field(default=3, ge=1, le=10)

class StudentLabAccessUpdate(BaseModel):
    access_expires_date: Optional[datetime] = None
    is_active: Optional[bool] = None
    can_create_vms: Optional[bool] = None
    can_delete_vms: Optional[bool] = None
    max_vms_allowed: Optional[int] = Field(None, ge=1, le=10)

class StudentLabAccessResponse(BaseModel):
    id: int
    student_id: int
    lab_id: int
    access_granted_date: datetime
    access_expires_date: Optional[datetime] = None
    is_active: bool
    first_access_date: Optional[datetime] = None
    last_access_date: Optional[datetime] = None
    total_access_count: int
    total_time_spent_minutes: int
    can_create_vms: bool
    can_delete_vms: bool
    max_vms_allowed: int
    created_at: datetime
    updated_at: datetime
    
    # Related data
    student_name: Optional[str] = None
    lab_name: Optional[str] = None

    class Config:
        from_attributes = True

# Student Dashboard Schemas
class StudentDashboardStats(BaseModel):
    total_assignments: int
    completed_assignments: int
    in_progress_assignments: int
    overdue_assignments: int
    total_labs: int
    active_labs: int
    total_vms: int
    running_vms: int
    average_score: Optional[float] = None
    total_time_spent_hours: float

class StudentAssignmentSummary(BaseModel):
    id: int
    title: str
    lab_name: str
    status: str
    progress_percentage: int
    due_date: Optional[datetime] = None
    score: Optional[int] = None
    max_score: int
    difficulty_level: str
    estimated_duration_minutes: int
    time_spent_minutes: int
    is_overdue: bool = False

class StudentLabSummary(BaseModel):
    id: int
    name: str
    description: Optional[str] = None
    instructor_name: Optional[str] = None
    assignment_count: int
    completed_assignments: int
    vm_count: int
    access_expires_date: Optional[datetime] = None
    last_access_date: Optional[datetime] = None
    total_time_spent_minutes: int

class StudentDashboardResponse(BaseModel):
    stats: StudentDashboardStats
    recent_assignments: List[StudentAssignmentSummary]
    active_labs: List[StudentLabSummary]
    recent_activity: List[ProgressLogResponse]

# Grading Schemas
class GradeSubmissionRequest(BaseModel):
    submission_id: int
    score: int = Field(..., ge=0, le=1000)
    feedback: Optional[str] = None

class BulkGradeRequest(BaseModel):
    assignment_id: int
    grades: List[Dict[str, Any]]  # List of {student_id, score, feedback}

class GradingStatsResponse(BaseModel):
    total_submissions: int
    graded_submissions: int
    pending_submissions: int
    average_score: Optional[float] = None
    grade_distribution: Dict[str, int]  # Grade ranges and counts
