from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .base import Base

class Notification(Base):
    __tablename__ = "notifications"

    id = Column(Integer, primary_key=True, index=True)
    
    # Recipient
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Notification details
    title = Column(String(200), nullable=False)
    message = Column(Text, nullable=False)
    notification_type = Column(String(50), nullable=False)  # vm_status, assignment, lab, system, etc.
    
    # Priority and category
    priority = Column(String(20), default='normal')  # low, normal, high, urgent
    category = Column(String(50), nullable=True)  # system, assignment, vm, lab, grade, etc.
    
    # Status
    is_read = Column(Boolean, default=False)
    is_dismissed = Column(Boolean, default=False)
    
    # Related entities
    related_entity_type = Column(String(50), nullable=True)  # vm, assignment, lab, user, etc.
    related_entity_id = Column(Integer, nullable=True)
    
    # Additional data
    data = Column(JSON, nullable=True)  # Additional context data
    
    # Action information
    action_url = Column(String(500), nullable=True)  # URL to navigate to when clicked
    action_text = Column(String(100), nullable=True)  # Text for action button
    
    # Expiration
    expires_at = Column(DateTime, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    read_at = Column(DateTime, nullable=True)
    dismissed_at = Column(DateTime, nullable=True)

    # Relationships
    user = relationship("User", back_populates="notifications")

class SystemAlert(Base):
    __tablename__ = "system_alerts"

    id = Column(Integer, primary_key=True, index=True)
    
    # Alert details
    title = Column(String(200), nullable=False)
    message = Column(Text, nullable=False)
    alert_type = Column(String(50), nullable=False)  # maintenance, outage, update, security, etc.
    
    # Severity and status
    severity = Column(String(20), default='info')  # info, warning, error, critical
    status = Column(String(20), default='active')  # active, resolved, scheduled
    
    # Targeting
    target_roles = Column(JSON, nullable=True)  # List of roles to show alert to
    target_users = Column(JSON, nullable=True)  # List of specific user IDs
    
    # Display settings
    is_dismissible = Column(Boolean, default=True)
    show_on_dashboard = Column(Boolean, default=True)
    show_as_banner = Column(Boolean, default=False)
    
    # Scheduling
    start_time = Column(DateTime, nullable=True)
    end_time = Column(DateTime, nullable=True)
    
    # Creator
    created_by_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Additional data
    data = Column(JSON, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    created_by = relationship("User")

class NotificationPreference(Base):
    __tablename__ = "notification_preferences"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Notification type preferences
    vm_status_notifications = Column(Boolean, default=True)
    assignment_notifications = Column(Boolean, default=True)
    lab_notifications = Column(Boolean, default=True)
    grade_notifications = Column(Boolean, default=True)
    system_notifications = Column(Boolean, default=True)
    
    # Delivery preferences
    email_notifications = Column(Boolean, default=False)
    push_notifications = Column(Boolean, default=True)
    desktop_notifications = Column(Boolean, default=True)
    
    # Frequency settings
    digest_frequency = Column(String(20), default='daily')  # immediate, hourly, daily, weekly
    quiet_hours_start = Column(String(5), nullable=True)  # HH:MM format
    quiet_hours_end = Column(String(5), nullable=True)  # HH:MM format
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="notification_preferences")

class ActivityLog(Base):
    __tablename__ = "activity_logs"

    id = Column(Integer, primary_key=True, index=True)
    
    # Actor
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)  # Nullable for system actions
    
    # Action details
    action = Column(String(100), nullable=False)  # login, logout, vm_created, assignment_submitted, etc.
    description = Column(Text, nullable=True)
    
    # Target entity
    entity_type = Column(String(50), nullable=True)  # vm, assignment, lab, user, etc.
    entity_id = Column(Integer, nullable=True)
    
    # Context
    ip_address = Column(String(45), nullable=True)  # IPv4 or IPv6
    user_agent = Column(String(500), nullable=True)
    session_id = Column(String(100), nullable=True)
    
    # Additional data
    context_data = Column(JSON, nullable=True)
    
    # Result
    success = Column(Boolean, default=True)
    error_message = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())

    # Relationships
    user = relationship("User")

class WebSocketSession(Base):
    __tablename__ = "websocket_sessions"

    id = Column(Integer, primary_key=True, index=True)
    
    # Session details
    session_id = Column(String(100), nullable=False, unique=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Connection details
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(String(500), nullable=True)
    
    # Status
    is_active = Column(Boolean, default=True)
    
    # Rooms joined
    rooms = Column(JSON, nullable=True)  # List of rooms the session is in
    
    # Timestamps
    connected_at = Column(DateTime, default=func.now())
    last_activity = Column(DateTime, default=func.now())
    disconnected_at = Column(DateTime, nullable=True)

    # Relationships
    user = relationship("User")

class RealTimeMetric(Base):
    __tablename__ = "realtime_metrics"

    id = Column(Integer, primary_key=True, index=True)
    
    # Metric details
    metric_name = Column(String(100), nullable=False, index=True)
    metric_value = Column(String(500), nullable=False)  # JSON string for complex values
    metric_type = Column(String(50), nullable=False)  # counter, gauge, histogram, etc.
    
    # Context
    entity_type = Column(String(50), nullable=True)  # vm, lab, user, system, etc.
    entity_id = Column(Integer, nullable=True)
    
    # Labels for filtering
    labels = Column(JSON, nullable=True)  # Key-value pairs for metric labels
    
    # Timestamps
    timestamp = Column(DateTime, default=func.now(), index=True)

class EventStream(Base):
    __tablename__ = "event_streams"

    id = Column(Integer, primary_key=True, index=True)
    
    # Event details
    event_type = Column(String(100), nullable=False, index=True)
    event_data = Column(JSON, nullable=False)
    
    # Source
    source_type = Column(String(50), nullable=False)  # user, system, vm, lab, etc.
    source_id = Column(Integer, nullable=True)
    
    # Target (for directed events)
    target_type = Column(String(50), nullable=True)
    target_id = Column(Integer, nullable=True)
    
    # Processing status
    processed = Column(Boolean, default=False)
    processed_at = Column(DateTime, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now(), index=True)

    # Indexes for efficient querying
    __table_args__ = (
        {'mysql_engine': 'InnoDB'}
    )
