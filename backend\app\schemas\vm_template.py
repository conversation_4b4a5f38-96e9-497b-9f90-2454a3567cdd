from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime

# VM Template Schemas
class VMTemplateBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    os_type: str = Field(..., min_length=1, max_length=50)
    os_version: Optional[str] = Field(None, max_length=100)
    default_cpu_cores: int = Field(default=2, ge=1, le=32)
    default_memory_mb: int = Field(default=2048, ge=512, le=32768)
    default_disk_size: int = Field(default=20, ge=5, le=500)
    source_type: str = Field(default='iso', regex='^(iso|existing_vm|cloud_image)$')
    source_path: Optional[str] = Field(None, max_length=500)
    template_config: Optional[Dict[str, Any]] = None
    network_config: Optional[Dict[str, Any]] = None
    installed_software: Optional[List[str]] = None
    is_active: bool = True
    is_public: bool = False

class VMTemplateCreate(VMTemplateBase):
    created_by_id: Optional[int] = None  # Will be set from current user if not provided

class VMTemplateUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    os_type: Optional[str] = Field(None, min_length=1, max_length=50)
    os_version: Optional[str] = Field(None, max_length=100)
    default_cpu_cores: Optional[int] = Field(None, ge=1, le=32)
    default_memory_mb: Optional[int] = Field(None, ge=512, le=32768)
    default_disk_size: Optional[int] = Field(None, ge=5, le=500)
    source_type: Optional[str] = Field(None, regex='^(iso|existing_vm|cloud_image)$')
    source_path: Optional[str] = Field(None, max_length=500)
    template_config: Optional[Dict[str, Any]] = None
    network_config: Optional[Dict[str, Any]] = None
    installed_software: Optional[List[str]] = None
    is_active: Optional[bool] = None
    is_public: Optional[bool] = None

class VMTemplateResponse(VMTemplateBase):
    id: int
    created_by_id: int
    created_by_name: Optional[str] = None
    build_status: str
    build_log: Optional[str] = None
    usage_count: int
    created_at: datetime
    updated_at: datetime
    last_used_at: Optional[datetime] = None
    categories: List[str] = []
    lab_count: int = 0

    class Config:
        from_attributes = True

class VMTemplateListResponse(BaseModel):
    templates: List[VMTemplateResponse]
    total: int
    page: int
    per_page: int
    total_pages: int

# Template Category Schemas
class TemplateCategoryBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    icon: Optional[str] = Field(None, max_length=50)
    color: Optional[str] = Field(None, regex='^#[0-9A-Fa-f]{6}$')
    is_active: bool = True
    sort_order: int = 0

class TemplateCategoryCreate(TemplateCategoryBase):
    pass

class TemplateCategoryUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    icon: Optional[str] = Field(None, max_length=50)
    color: Optional[str] = Field(None, regex='^#[0-9A-Fa-f]{6}$')
    is_active: Optional[bool] = None
    sort_order: Optional[int] = None

class TemplateCategoryResponse(TemplateCategoryBase):
    id: int
    template_count: int = 0
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Template Version Schemas
class TemplateVersionBase(BaseModel):
    version_number: str = Field(..., min_length=1, max_length=20)
    version_name: Optional[str] = Field(None, max_length=100)
    changelog: Optional[str] = None
    config_snapshot: Dict[str, Any]
    is_active: bool = True

class TemplateVersionCreate(TemplateVersionBase):
    template_id: int

class TemplateVersionUpdate(BaseModel):
    version_name: Optional[str] = Field(None, max_length=100)
    changelog: Optional[str] = None
    is_active: Optional[bool] = None

class TemplateVersionResponse(TemplateVersionBase):
    id: int
    template_id: int
    is_latest: bool
    build_status: str
    build_log: Optional[str] = None
    build_started_at: Optional[datetime] = None
    build_completed_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Template Usage Schemas
class TemplateUsageLogResponse(BaseModel):
    id: int
    template_id: int
    template_name: Optional[str] = None
    user_id: int
    user_name: Optional[str] = None
    vm_id: Optional[int] = None
    vm_name: Optional[str] = None
    lab_id: Optional[int] = None
    lab_name: Optional[str] = None
    action: str
    success: bool
    error_message: Optional[str] = None
    creation_time_seconds: Optional[int] = None
    created_at: datetime

    class Config:
        from_attributes = True

# Template Statistics
class TemplateStatsResponse(BaseModel):
    total_templates: int
    active_templates: int
    inactive_templates: int
    public_templates: int
    private_templates: int
    total_categories: int
    total_usage_count: int
    most_used_templates: List[Dict[str, Any]] = []

# Template Assignment
class AssignTemplatesToLabRequest(BaseModel):
    lab_id: int
    template_ids: List[int]

class CreateVMFromTemplateRequest(BaseModel):
    template_id: int
    vm_name: str = Field(..., min_length=1, max_length=100)
    cpu_cores: Optional[int] = Field(None, ge=1, le=32)
    memory_mb: Optional[int] = Field(None, ge=512, le=32768)
    disk_size: Optional[int] = Field(None, ge=5, le=500)
    lab_id: Optional[int] = None
    custom_config: Optional[Dict[str, Any]] = None

class BulkTemplateResponse(BaseModel):
    success_count: int
    failed_count: int
    errors: List[str] = []

# Template Build
class TemplateBuildRequest(BaseModel):
    template_id: int
    version_number: Optional[str] = None
    force_rebuild: bool = False

class TemplateBuildResponse(BaseModel):
    build_id: str
    status: str
    message: str
    estimated_time_minutes: Optional[int] = None
