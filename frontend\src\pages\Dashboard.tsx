import React from 'react';
import { useSelector } from 'react-redux';
import {
  Box,
  Container,
  Grid,
  Typography,
  CircularProgress,
  Alert,
  Fade,
  useTheme,
} from '@mui/material';
import {
  Memory as MemoryIcon,
  Storage as StorageIcon,
  Dns as CpuIcon,
  Refresh,
} from '@mui/icons-material';
import { RootState } from '../types/store';
import { useSystemMetrics } from '../hooks/useSystemMetrics';
import SystemMetricsCard from '../components/Dashboard/SystemMetricsCard';
import SystemUsageChart from '../components/Dashboard/SystemUsageChart';
import SystemOverview from '../components/Dashboard/SystemOverview';

const Dashboard = () => {
  const { user } = useSelector((state: RootState) => state.auth);
  const theme = useTheme();

  const { metrics, historicalMetrics, isLoading, error, refreshMetrics } =
    useSystemMetrics({
      refreshInterval: 30000,
      autoRefresh: true,
    });

  if (isLoading && !metrics) {
    return (
      <Container maxWidth="xl">
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '60vh',
            flexDirection: 'column',
            gap: 2,
          }}
        >
          <CircularProgress size={60} />
          <Typography variant="h6" color="textSecondary">
            Loading system metrics...
          </Typography>
        </Box>
      </Container>
    );
  }

  // Don't return early on error, show faded content instead

  // Create mock data when metrics are not available
  const mockMetrics = {
    timestamp: new Date().toISOString(),
    cluster: {
      total_nodes: 0,
      online_nodes: 0,
      health: 'unknown',
    },
    resources: {
      cpu: { usage_percentage: 0, status: 'unknown' },
      memory: { usage_percentage: 0, status: 'unknown' },
      disk: { usage_percentage: 0, status: 'unknown' },
    },
    virtual_machines: {
      total_vms: 0,
      running_vms: 0,
      stopped_vms: 0,
      suspended_vms: 0,
      vm_types: { kvm: 0, lxc: 0 },
      resource_allocation: {
        total_cpu_cores: 0,
        total_memory_mb: 0,
        total_disk_gb: 0,
      },
    },
    nodes: [],
  };

  const displayMetrics = metrics || mockMetrics;
  const hasError = !!error || !metrics;

  return (
    <Container maxWidth="xl">
      <Fade in timeout={800}>
        <Box
          sx={{
            opacity: hasError ? 0.6 : 1,
            transition: 'opacity 0.3s ease-in-out',
            position: 'relative',
          }}
        >
          {/* Error Banner */}
          {hasError && (
            <Alert
              severity="error"
              action={
                <Box sx={{ cursor: 'pointer' }} onClick={refreshMetrics}>
                  <Refresh />
                </Box>
              }
              sx={{
                mb: 3,
                borderRadius: 2,
                '& .MuiAlert-message': {
                  fontWeight: 500,
                },
              }}
            >
              {error || 'Unable to connect to the server. Showing cached data.'}
            </Alert>
          )}

          {/* Header */}
          <Box
            sx={{
              mb: 4,
              background: `linear-gradient(135deg, ${theme.palette.primary.main}20 0%, ${theme.palette.secondary.main}20 100%)`,
              borderRadius: 3,
              p: 4,
              border: `1px solid ${theme.palette.divider}`,
            }}
          >
            <Typography
              variant="h3"
              fontWeight={700}
              color="textPrimary"
              gutterBottom
            >
              Welcome back, {user?.username}! 👋
            </Typography>
            <Typography
              variant="h6"
              color="textSecondary"
              fontWeight={400}
              sx={{ mb: 2 }}
            >
              Here's what's happening with your virtual lab environment
            </Typography>
            <Typography
              variant="body2"
              color="textSecondary"
              sx={{ opacity: 0.8 }}
            >
              Last updated:{' '}
              {new Date(displayMetrics.timestamp).toLocaleString()}
              {hasError && ' (Cached)'}
            </Typography>
          </Box>

          <Grid container spacing={4}>
            {/* System Overview */}
            <Grid item xs={12}>
              <SystemOverview systemInfo={displayMetrics} />
            </Grid>

            {/* Resource Usage Cards */}
            <Grid item xs={12} sm={6} md={4}>
              <SystemMetricsCard
                title="CPU Usage"
                value={displayMetrics.resources.cpu.usage_percentage}
                unit="%"
                icon={<CpuIcon sx={{ fontSize: 32 }} />}
                color="primary"
                status={
                  hasError
                    ? 'critical'
                    : displayMetrics.resources.cpu.status === 'normal'
                    ? 'normal'
                    : 'warning'
                }
                trend="up"
                trendValue={2.3}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <SystemMetricsCard
                title="Memory Usage"
                value={displayMetrics.resources.memory.usage_percentage}
                unit="%"
                icon={<MemoryIcon sx={{ fontSize: 32 }} />}
                color="secondary"
                status={
                  hasError
                    ? 'critical'
                    : displayMetrics.resources.memory.status === 'normal'
                    ? 'normal'
                    : 'warning'
                }
                trend="down"
                trendValue={-1.2}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <SystemMetricsCard
                title="Disk Usage"
                value={displayMetrics.resources.disk.usage_percentage}
                unit="%"
                icon={<StorageIcon sx={{ fontSize: 32 }} />}
                color="warning"
                status={
                  hasError
                    ? 'critical'
                    : displayMetrics.resources.disk.status === 'normal'
                    ? 'normal'
                    : 'warning'
                }
                trend="flat"
                trendValue={0.1}
              />
            </Grid>

            {/* Historical Usage Chart */}
            {historicalMetrics && (
              <Grid item xs={12}>
                <SystemUsageChart
                  title="System Resource Usage (Last 24 Hours)"
                  data={{
                    timestamps: historicalMetrics.timestamps,
                    cpu_usage: historicalMetrics.metrics.cpu_usage,
                    memory_usage: historicalMetrics.metrics.memory_usage,
                    disk_usage: historicalMetrics.metrics.disk_usage,
                  }}
                  height={400}
                />
              </Grid>
            )}
          </Grid>

          {/* Bottom Error Message */}
          {hasError && (
            <Box
              sx={{
                position: 'fixed',
                bottom: 20,
                left: '50%',
                transform: 'translateX(-50%)',
                zIndex: 1000,
                animation: 'slideUp 0.3s ease-out',
                '@keyframes slideUp': {
                  from: {
                    opacity: 0,
                    transform: 'translateX(-50%) translateY(20px)',
                  },
                  to: {
                    opacity: 1,
                    transform: 'translateX(-50%) translateY(0)',
                  },
                },
              }}
            >
              <Alert
                severity="warning"
                variant="filled"
                sx={{
                  borderRadius: 3,
                  boxShadow: theme.shadows[8],
                  minWidth: 300,
                  '& .MuiAlert-message': {
                    fontWeight: 500,
                  },
                }}
                action={
                  <Box
                    sx={{
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      color: 'inherit',
                    }}
                    onClick={refreshMetrics}
                  >
                    <Refresh sx={{ fontSize: 20 }} />
                  </Box>
                }
              >
                Connection lost. Click to retry.
              </Alert>
            </Box>
          )}
        </Box>
      </Fade>
    </Container>
  );
};

export default Dashboard;
