"""Enhance assignment system with progress tracking

Revision ID: f15e2cc5adaf
Revises: aadde34a51f8
Create Date: 2025-07-09 09:28:28.895418

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f15e2cc5adaf'
down_revision = 'aadde34a51f8'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('student_lab_access',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('student_id', sa.Integer(), nullable=False),
    sa.Column('lab_id', sa.Integer(), nullable=False),
    sa.Column('access_granted_date', sa.DateTime(), nullable=True),
    sa.Column('access_expires_date', sa.DateTime(), nullable=True),
    sa.Column('is_active', sa.<PERSON>(), nullable=True),
    sa.Column('first_access_date', sa.DateTime(), nullable=True),
    sa.Column('last_access_date', sa.DateTime(), nullable=True),
    sa.Column('total_access_count', sa.Integer(), nullable=True),
    sa.Column('total_time_spent_minutes', sa.Integer(), nullable=True),
    sa.Column('can_create_vms', sa.Boolean(), nullable=True),
    sa.Column('can_delete_vms', sa.Boolean(), nullable=True),
    sa.Column('max_vms_allowed', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['lab_id'], ['labs.id'], ),
    sa.ForeignKeyConstraint(['student_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_student_lab_access_id'), 'student_lab_access', ['id'], unique=False)
    op.create_table('assignment_progress_logs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('assignment_id', sa.Integer(), nullable=False),
    sa.Column('student_id', sa.Integer(), nullable=False),
    sa.Column('action', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('progress_percentage', sa.Integer(), nullable=True),
    sa.Column('vm_id', sa.Integer(), nullable=True),
    sa.Column('step_number', sa.Integer(), nullable=True),
    sa.Column('context_data', sa.JSON(), nullable=True),
    sa.Column('session_duration_minutes', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['assignment_id'], ['lab_assignments.id'], ),
    sa.ForeignKeyConstraint(['student_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['vm_id'], ['virtual_machines.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_assignment_progress_logs_id'), 'assignment_progress_logs', ['id'], unique=False)
    op.create_table('assignment_submissions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('assignment_id', sa.Integer(), nullable=False),
    sa.Column('student_id', sa.Integer(), nullable=False),
    sa.Column('submission_type', sa.String(length=50), nullable=True),
    sa.Column('content', sa.Text(), nullable=True),
    sa.Column('file_path', sa.String(length=500), nullable=True),
    sa.Column('file_name', sa.String(length=255), nullable=True),
    sa.Column('file_size', sa.Integer(), nullable=True),
    sa.Column('vm_snapshot_id', sa.String(length=100), nullable=True),
    sa.Column('vm_config', sa.JSON(), nullable=True),
    sa.Column('submission_notes', sa.Text(), nullable=True),
    sa.Column('is_final', sa.Boolean(), nullable=True),
    sa.Column('version', sa.Integer(), nullable=True),
    sa.Column('is_graded', sa.Boolean(), nullable=True),
    sa.Column('score', sa.Integer(), nullable=True),
    sa.Column('feedback', sa.Text(), nullable=True),
    sa.Column('graded_by_id', sa.Integer(), nullable=True),
    sa.Column('graded_date', sa.DateTime(), nullable=True),
    sa.Column('submitted_date', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['assignment_id'], ['lab_assignments.id'], ),
    sa.ForeignKeyConstraint(['graded_by_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['student_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_assignment_submissions_id'), 'assignment_submissions', ['id'], unique=False)
    op.add_column('lab_assignments', sa.Column('assignment_type', sa.String(length=50), nullable=True))
    op.add_column('lab_assignments', sa.Column('difficulty_level', sa.String(length=20), nullable=True))
    op.add_column('lab_assignments', sa.Column('estimated_duration_minutes', sa.Integer(), nullable=True))
    op.add_column('lab_assignments', sa.Column('required_templates', sa.JSON(), nullable=True))
    op.add_column('lab_assignments', sa.Column('required_vms', sa.Integer(), nullable=True))
    op.add_column('lab_assignments', sa.Column('status', sa.String(length=20), nullable=True))
    op.add_column('lab_assignments', sa.Column('max_score', sa.Integer(), nullable=True))
    op.add_column('lab_assignments', sa.Column('score', sa.Integer(), nullable=True))
    op.add_column('lab_assignments', sa.Column('feedback', sa.Text(), nullable=True))
    op.add_column('lab_assignments', sa.Column('auto_grade', sa.Boolean(), nullable=True))
    op.add_column('lab_assignments', sa.Column('time_spent_minutes', sa.Integer(), nullable=True))
    op.add_column('lab_assignments', sa.Column('attempts_count', sa.Integer(), nullable=True))
    op.add_column('lab_assignments', sa.Column('last_activity', sa.DateTime(), nullable=True))
    op.add_column('lab_assignments', sa.Column('submitted_date', sa.DateTime(), nullable=True))
    op.add_column('lab_assignments', sa.Column('graded_date', sa.DateTime(), nullable=True))
    op.drop_constraint(op.f('virtual_machines_ibfk_1'), 'virtual_machines', type_='foreignkey')
    op.drop_index(op.f('ix_virtual_machines_owner_id'), table_name='virtual_machines')
    op.create_index(op.f('ix_virtual_machines_id'), 'virtual_machines', ['id'], unique=False)
    op.create_foreign_key(None, 'virtual_machines', 'vm_templates', ['template_id'], ['id'])
    op.create_foreign_key(None, 'virtual_machines', 'users', ['owner_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'virtual_machines', type_='foreignkey')
    op.drop_constraint(None, 'virtual_machines', type_='foreignkey')
    op.create_foreign_key(op.f('virtual_machines_ibfk_1'), 'virtual_machines', 'users', ['owner_id'], ['id'], ondelete='CASCADE')
    op.drop_index(op.f('ix_virtual_machines_id'), table_name='virtual_machines')
    op.create_index(op.f('ix_virtual_machines_owner_id'), 'virtual_machines', ['owner_id'], unique=False)
    op.drop_column('lab_assignments', 'graded_date')
    op.drop_column('lab_assignments', 'submitted_date')
    op.drop_column('lab_assignments', 'last_activity')
    op.drop_column('lab_assignments', 'attempts_count')
    op.drop_column('lab_assignments', 'time_spent_minutes')
    op.drop_column('lab_assignments', 'auto_grade')
    op.drop_column('lab_assignments', 'feedback')
    op.drop_column('lab_assignments', 'score')
    op.drop_column('lab_assignments', 'max_score')
    op.drop_column('lab_assignments', 'status')
    op.drop_column('lab_assignments', 'required_vms')
    op.drop_column('lab_assignments', 'required_templates')
    op.drop_column('lab_assignments', 'estimated_duration_minutes')
    op.drop_column('lab_assignments', 'difficulty_level')
    op.drop_column('lab_assignments', 'assignment_type')
    op.drop_index(op.f('ix_assignment_submissions_id'), table_name='assignment_submissions')
    op.drop_table('assignment_submissions')
    op.drop_index(op.f('ix_assignment_progress_logs_id'), table_name='assignment_progress_logs')
    op.drop_table('assignment_progress_logs')
    op.drop_index(op.f('ix_student_lab_access_id'), table_name='student_lab_access')
    op.drop_table('student_lab_access')
    # ### end Alembic commands ###
