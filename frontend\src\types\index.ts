import { Action } from 'redux';
import { ThunkAction, ThunkDispatch } from 'redux-thunk';

export type RootState = {
  auth: {
    user: User | null;
    token: string | null;
    isLoading: boolean;
    error: ApiError | null;
  };
  vm: {
    vms: VM[];
    selectedVM: VM | null;
    isLoading: boolean;
    error: ApiError | null;
  };
};

export type AppThunk<ReturnType = void> = ThunkAction<
  Promise<ReturnType>,
  RootState,
  unknown,
  Action<string>
>;

export type AppDispatch = ThunkDispatch<RootState, unknown, Action<string>>;

export interface ThunkConfig {
  /** return value when rejected */
  rejectValue: ApiError;
  /** state type */
  state: RootState;
}

export enum VMType {
  KVM = 'kvm',
  LXC = 'lxc',
}

export enum UserRole {
  STUDENT = 'STUDENT',
  TEACHER = 'TEACHER',
  ADMIN = 'ADMIN',
}

export type VMStatus = 'RUNNING' | 'STOPPED' | 'SUSPENDED' | 'FAILED';

export interface VM {
  id: number;
  name: string;
  vm_type: VMType;
  status: VMStatus;
  proxmox_id: number;
  proxmox_node: string;
  cpu_cores: number;
  memory_mb: number;
  disk_size: number;
  ip_address?: string;
  mac_address?: string;
  rdp_enabled: boolean;
  ssh_enabled: boolean;
  ssh_port?: number;
  rdp_port?: number;
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  owner_id: number;
  owner_name?: string;
}

export interface TokenResponse {
  access_token: string;
  token_type: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface VMUpdateData {
  name?: string;
  cpu_cores?: number;
  memory_mb?: number;
  disk_size?: number;
  rdp_enabled?: boolean;
  ssh_enabled?: boolean;
}

export interface VMActionData {
  action: 'start' | 'stop' | 'restart' | 'suspend' | 'delete';
}

export interface ProfileUpdateData {
  email?: string;
  fullName?: string;
  currentPassword?: string;
  newPassword?: string;
}

export interface VMCreateData {
  name: string;
  vm_type: VMType;
  cpu_cores: number;
  memory_mb: number;
  disk_size: number;
  rdp_enabled?: boolean;
  ssh_enabled?: boolean;
}

export interface User {
  id: number;
  username: string;
  email: string;
  role: 'STUDENT' | 'TEACHER' | 'ADMIN';
  fullName?: string;
}

export interface ApiError {
  message: string;
  status?: number;
  errors?: Record<string, string[]>;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  error: ApiError | null;
}

export interface VMState {
  vms: VM[];
  selectedVM: VM | null;
  isLoading: boolean;
  error: ApiError | null;
}

export interface VMMetricsResponse {
  current: {
    cpu_usage: number;
    memory_usage: number;
    disk_usage: number;
    network_usage: number;
  };
  history: Array<{
    timestamp: string;
    cpu_usage: number;
    memory_usage: number;
    disk_usage: number;
    network_usage: number;
  }>;
}

export interface VMHistoricalData {
  timestamps: string[];
  cpu: number[];
  memory: number[];
  disk: number[];
  network: number[];
}

export interface AsyncThunkConfig {
  rejectValue: ApiError;
  state: { auth: AuthState; vm: VMState; lab: LabState };
}

// Lab Management Types
export interface Lab {
  id: number;
  name: string;
  description?: string;
  instructor_id: number;
  instructor_name?: string;
  max_students: number;
  max_vms_per_student: number;
  is_active: boolean;
  start_date?: string;
  end_date?: string;
  student_count: number;
  vm_count: number;
  assignment_count: number;
  created_at: string;
  updated_at: string;
}

export interface LabCreateData {
  name: string;
  description?: string;
  instructor_id?: number;
  max_students: number;
  max_vms_per_student: number;
  is_active: boolean;
  start_date?: string;
  end_date?: string;
}

export interface LabUpdateData {
  name?: string;
  description?: string;
  max_students?: number;
  max_vms_per_student?: number;
  is_active?: boolean;
  start_date?: string;
  end_date?: string;
}

export interface LabListResponse {
  labs: Lab[];
  total: number;
  page: number;
  per_page: number;
  total_pages: number;
}

export interface StudentGroup {
  id: number;
  name: string;
  description?: string;
  instructor_id: number;
  instructor_name?: string;
  max_members: number;
  is_active: boolean;
  member_count: number;
  created_at: string;
  updated_at: string;
}

export interface StudentGroupCreateData {
  name: string;
  description?: string;
  instructor_id?: number;
  max_members: number;
  is_active: boolean;
}

export interface StudentGroupUpdateData {
  name?: string;
  description?: string;
  max_members?: number;
  is_active?: boolean;
}

export interface StudentGroupListResponse {
  groups: StudentGroup[];
  total: number;
  page: number;
  per_page: number;
  total_pages: number;
}

export interface LabAssignment {
  id: number;
  lab_id: number;
  student_id: number;
  vm_id?: number;
  title: string;
  description?: string;
  instructions?: string;
  is_active: boolean;
  is_completed: boolean;
  completion_date?: string;
  progress_percentage: number;
  notes?: string;
  lab_name?: string;
  student_name?: string;
  vm_name?: string;
  created_at: string;
  updated_at: string;
}

export interface LabAssignmentCreateData {
  lab_id: number;
  student_id: number;
  vm_id?: number;
  title: string;
  description?: string;
  instructions?: string;
  is_active: boolean;
}

export interface LabAssignmentUpdateData {
  title?: string;
  description?: string;
  instructions?: string;
  is_active?: boolean;
  is_completed?: boolean;
  progress_percentage?: number;
  notes?: string;
}

export interface LabStats {
  total_labs: number;
  active_labs: number;
  inactive_labs: number;
  total_assignments: number;
  completed_assignments: number;
  pending_assignments: number;
  total_students_enrolled: number;
  total_groups: number;
}

export interface AssignStudentsToLabRequest {
  lab_id: number;
  student_ids: number[];
}

export interface AssignVMsToLabRequest {
  lab_id: number;
  vm_ids: number[];
}

export interface BulkAssignmentResponse {
  success_count: number;
  failed_count: number;
  errors: string[];
}

export interface LabState {
  labs: Lab[];
  selectedLab: Lab | null;
  groups: StudentGroup[];
  selectedGroup: StudentGroup | null;
  assignments: LabAssignment[];
  stats: LabStats | null;
  isLoading: boolean;
  error: ApiError | null;
}
