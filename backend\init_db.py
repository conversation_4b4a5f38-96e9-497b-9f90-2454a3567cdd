#!/usr/bin/env python3
"""
Database initialization script for the Virtual Lab Management System.
Creates tables and adds a default admin user.
"""

import sys
import os
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from app.database import get_db, engine
from app.models import Base, User
from app.routers.auth import get_password_hash
from sqlalchemy.orm import Session

def create_default_admin():
    """Create a default admin user if none exists."""
    db = next(get_db())
    try:
        # Check if admin user already exists
        admin_user = db.query(User).filter(User.username == "admin").first()
        if admin_user:
            print("✅ Admin user already exists")
            return
        
        # Create default admin user
        hashed_password = get_password_hash("admin123")
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            full_name="System Administrator",
            hashed_password=hashed_password,
            role="ADMIN",
            is_active=True
        )
        
        db.add(admin_user)
        db.commit()
        print("✅ Created default admin user:")
        print("   Username: admin")
        print("   Password: admin123")
        print("   Email: <EMAIL>")
        
    except Exception as e:
        print(f"❌ Error creating admin user: {e}")
        db.rollback()
    finally:
        db.close()

def main():
    """Initialize the database and create default data."""
    print("🚀 Initializing Virtual Lab Management Database...")
    
    try:
        # Initialize database tables
        print("📊 Creating database tables...")
        Base.metadata.create_all(bind=engine)
        print("✅ Database tables created successfully")
        
        # Create default admin user
        print("👤 Creating default admin user...")
        create_default_admin()
        
        print("\n🎉 Database initialization completed successfully!")
        print("\n📝 Next steps:")
        print("1. Start the backend server: python -m uvicorn app.main:app --reload")
        print("2. Start the frontend: cd frontend && npm start")
        print("3. Login with admin/admin123 to access the system")
        
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
