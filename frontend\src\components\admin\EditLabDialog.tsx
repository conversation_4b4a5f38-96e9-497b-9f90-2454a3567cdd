import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Title,
  DialogContent,
  <PERSON>alogActions,
  Button,
  TextField,
  FormControlLabel,
  Switch,
  Grid,
  Alert,
  Box,
  Typography,
} from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import api from '../../services/api';

interface Lab {
  id: number;
  name: string;
  description: string | null;
  max_students: number;
  max_vms_per_student: number;
  is_active: boolean;
  start_date: string | null;
  end_date: string | null;
}

interface EditLabDialogProps {
  open: boolean;
  lab: Lab | null;
  onClose: () => void;
  onLabUpdated: () => void;
}

interface EditLabFormData {
  name: string;
  description: string;
  max_students: number;
  max_vms_per_student: number;
  is_active: boolean;
  start_date: Date | null;
  end_date: Date | null;
}

const EditLabDialog: React.FC<EditLabDialogProps> = ({
  open,
  lab,
  onClose,
  onLabUpdated,
}) => {
  const [formData, setFormData] = useState<EditLabFormData>({
    name: '',
    description: '',
    max_students: 30,
    max_vms_per_student: 3,
    is_active: true,
    start_date: null,
    end_date: null,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  useEffect(() => {
    if (lab) {
      setFormData({
        name: lab.name,
        description: lab.description || '',
        max_students: lab.max_students,
        max_vms_per_student: lab.max_vms_per_student,
        is_active: lab.is_active,
        start_date: lab.start_date ? new Date(lab.start_date) : null,
        end_date: lab.end_date ? new Date(lab.end_date) : null,
      });
    }
  }, [lab]);

  const handleInputChange = (field: keyof EditLabFormData) => (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | any
  ) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleDateChange = (field: 'start_date' | 'end_date') => (date: Date | null) => {
    setFormData(prev => ({ ...prev, [field]: date }));
    
    // Clear error when user changes date
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Lab name is required';
    } else if (formData.name.length < 3) {
      newErrors.name = 'Lab name must be at least 3 characters';
    }

    if (formData.max_students < 1 || formData.max_students > 100) {
      newErrors.max_students = 'Max students must be between 1 and 100';
    }

    if (formData.max_vms_per_student < 1 || formData.max_vms_per_student > 10) {
      newErrors.max_vms_per_student = 'Max VMs per student must be between 1 and 10';
    }

    if (formData.start_date && formData.end_date && formData.start_date >= formData.end_date) {
      newErrors.end_date = 'End date must be after start date';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm() || !lab) return;

    setLoading(true);
    setSubmitError(null);

    try {
      const updateData = {
        name: formData.name,
        description: formData.description || undefined,
        max_students: formData.max_students,
        max_vms_per_student: formData.max_vms_per_student,
        is_active: formData.is_active,
        start_date: formData.start_date?.toISOString(),
        end_date: formData.end_date?.toISOString(),
      };

      await api.updateLab(lab.id, updateData);

      onLabUpdated();
      handleClose();
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : 'Failed to update lab');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setFormData({
      name: '',
      description: '',
      max_students: 30,
      max_vms_per_student: 3,
      is_active: true,
      start_date: null,
      end_date: null,
    });
    setErrors({});
    setSubmitError(null);
    onClose();
  };

  if (!lab) return null;

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
        <DialogTitle>
          <Typography variant="h6" fontWeight={600}>
            Edit Lab: {lab.name}
          </Typography>
        </DialogTitle>
        
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            {submitError && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {submitError}
              </Alert>
            )}

            <Grid container spacing={2}>
              <Grid item xs={12} sm={8}>
                <TextField
                  fullWidth
                  label="Lab Name"
                  value={formData.name}
                  onChange={handleInputChange('name')}
                  error={!!errors.name}
                  helperText={errors.name}
                  required
                />
              </Grid>
              
              <Grid item xs={12} sm={4}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.is_active}
                      onChange={handleInputChange('is_active')}
                    />
                  }
                  label="Active Lab"
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  multiline
                  rows={3}
                  value={formData.description}
                  onChange={handleInputChange('description')}
                  error={!!errors.description}
                  helperText={errors.description}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Max Students"
                  type="number"
                  value={formData.max_students}
                  onChange={handleInputChange('max_students')}
                  error={!!errors.max_students}
                  helperText={errors.max_students}
                  inputProps={{ min: 1, max: 100 }}
                  required
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Max VMs per Student"
                  type="number"
                  value={formData.max_vms_per_student}
                  onChange={handleInputChange('max_vms_per_student')}
                  error={!!errors.max_vms_per_student}
                  helperText={errors.max_vms_per_student}
                  inputProps={{ min: 1, max: 10 }}
                  required
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <DateTimePicker
                  label="Start Date (Optional)"
                  value={formData.start_date}
                  onChange={handleDateChange('start_date')}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      fullWidth
                      error={!!errors.start_date}
                      helperText={errors.start_date}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <DateTimePicker
                  label="End Date (Optional)"
                  value={formData.end_date}
                  onChange={handleDateChange('end_date')}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      fullWidth
                      error={!!errors.end_date}
                      helperText={errors.end_date}
                    />
                  )}
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>

        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button onClick={handleClose} disabled={loading}>
            Cancel
          </Button>
          <LoadingButton
            onClick={handleSubmit}
            loading={loading}
            variant="contained"
          >
            Update Lab
          </LoadingButton>
        </DialogActions>
      </Dialog>
    </LocalizationProvider>
  );
};

export default EditLabDialog;
