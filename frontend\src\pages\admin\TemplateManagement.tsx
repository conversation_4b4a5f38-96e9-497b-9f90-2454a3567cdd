import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Container,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Pagination,
  Tooltip,
  Alert,
  CircularProgress,
  useTheme,
  Avatar,
  Stack,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Computer as ComputerIcon,
  Build as BuildIcon,
  Refresh as RefreshIcon,
  CloudDownload as CloudDownloadIcon,
  Storage as StorageIcon,
  Memory as MemoryIcon,
  Speed as SpeedIcon,
  Public as PublicIcon,
  Lock as LockIcon,
  PlayArrow as PlayArrowIcon,
  Stop as StopIcon,
  Category as CategoryIcon,
} from '@mui/icons-material';
import { useSelector } from 'react-redux';
import { RootState } from '../../types/store';
import { UserRole } from '../../types';
import api from '../../services/api';

interface VMTemplate {
  id: number;
  name: string;
  description: string | null;
  created_by_id: number;
  created_by_name: string | null;
  os_type: string;
  os_version: string | null;
  default_cpu_cores: number;
  default_memory_mb: number;
  default_disk_size: number;
  source_type: string;
  source_path: string | null;
  template_config: any;
  network_config: any;
  installed_software: string[] | null;
  is_active: boolean;
  is_public: boolean;
  build_status: string;
  build_log: string | null;
  usage_count: number;
  created_at: string;
  updated_at: string;
  last_used_at: string | null;
  categories: string[];
  lab_count: number;
}

interface TemplateStats {
  total_templates: number;
  active_templates: number;
  inactive_templates: number;
  public_templates: number;
  private_templates: number;
  total_categories: number;
  total_usage_count: number;
  most_used_templates: Array<{
    id: number;
    name: string;
    usage_count: number;
    os_type: string;
  }>;
}

const TemplateManagement: React.FC = () => {
  const theme = useTheme();
  const { user } = useSelector((state: RootState) => state.auth);

  // State
  const [templates, setTemplates] = useState<VMTemplate[]>([]);
  const [stats, setStats] = useState<TemplateStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Filters and pagination
  const [search, setSearch] = useState('');
  const [osTypeFilter, setOsTypeFilter] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [visibilityFilter, setVisibilityFilter] = useState<string>('');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);
  const perPage = 10;

  // Check if current user can manage templates
  const canManageTemplates =
    (user?.role as UserRole) === UserRole.ADMIN || (user?.role as UserRole) === UserRole.TEACHER;

  useEffect(() => {
    if (!canManageTemplates) {
      setError('Instructor or admin access required');
      setLoading(false);
      return;
    }

    fetchTemplates();
    fetchStats();
  }, [
    canManageTemplates,
    page,
    search,
    osTypeFilter,
    statusFilter,
    visibilityFilter,
  ]);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      const params: any = {
        page,
        per_page: perPage,
      };

      if (search) params.search = search;
      if (osTypeFilter) params.os_type = osTypeFilter;
      if (statusFilter !== '') params.is_active = statusFilter === 'true';
      if (visibilityFilter !== '')
        params.is_public = visibilityFilter === 'true';

      const data = await api.getTemplates(params);
      setTemplates(data.templates);
      setTotal(data.total);
      setTotalPages(data.total_pages);
      setError(null);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : 'Failed to fetch templates'
      );
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const data = await api.getTemplateStats();
      setStats(data);
    } catch (err) {
      console.error('Failed to fetch template stats:', err);
    }
  };

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(event.target.value);
    setPage(1); // Reset to first page when searching
  };

  const handleOsTypeFilter = (event: any) => {
    setOsTypeFilter(event.target.value);
    setPage(1);
  };

  const handleStatusFilter = (event: any) => {
    setStatusFilter(event.target.value);
    setPage(1);
  };

  const handleVisibilityFilter = (event: any) => {
    setVisibilityFilter(event.target.value);
    setPage(1);
  };

  const handlePageChange = (
    event: React.ChangeEvent<unknown>,
    value: number
  ) => {
    setPage(value);
  };

  const handleCreateTemplate = () => {
    // TODO: Open create template dialog
  };

  const handleEditTemplate = (template: VMTemplate) => {
    // TODO: Open edit template dialog
  };

  const handleDeleteTemplate = async (template: VMTemplate) => {
    if (
      window.confirm(
        `Are you sure you want to delete template "${template.name}"? This action cannot be undone.`
      )
    ) {
      try {
        await api.deleteTemplate(template.id);
        fetchTemplates(); // Refresh the list
        fetchStats(); // Refresh stats
      } catch (error) {
        setError(
          error instanceof Error ? error.message : 'Failed to delete template'
        );
      }
    }
  };

  const handleToggleTemplateStatus = async (template: VMTemplate) => {
    try {
      await api.updateTemplate(template.id, { is_active: !template.is_active });
      fetchTemplates(); // Refresh the list
    } catch (error) {
      setError(
        error instanceof Error
          ? error.message
          : 'Failed to update template status'
      );
    }
  };

  const getOSIcon = (osType: string) => {
    switch (osType.toLowerCase()) {
      case 'linux':
        return '🐧';
      case 'windows':
        return '🪟';
      case 'macos':
        return '🍎';
      default:
        return '💻';
    }
  };

  const getBuildStatusColor = (status: string) => {
    switch (status) {
      case 'ready':
        return 'success';
      case 'building':
        return 'warning';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  if (!canManageTemplates) {
    return (
      <Container maxWidth="lg">
        <Alert severity="error" sx={{ mt: 4 }}>
          Instructor or admin access required to view this page.
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl">
      <Box sx={{ py: 4 }}>
        {/* Header */}
        <Box
          sx={{
            mb: 4,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Typography variant="h4" fontWeight={700}>
            VM Template Management
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateTemplate}
          >
            Create Template
          </Button>
        </Box>

        {/* Stats Cards */}
        {stats && (
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <StorageIcon color="primary" sx={{ fontSize: 40 }} />
                    <Box>
                      <Typography variant="h4" fontWeight={700}>
                        {stats.total_templates}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Total Templates
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <PlayArrowIcon color="success" sx={{ fontSize: 40 }} />
                    <Box>
                      <Typography variant="h4" fontWeight={700}>
                        {stats.active_templates}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Active Templates
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <PublicIcon color="info" sx={{ fontSize: 40 }} />
                    <Box>
                      <Typography variant="h4" fontWeight={700}>
                        {stats.public_templates}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Public Templates
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <CloudDownloadIcon color="warning" sx={{ fontSize: 40 }} />
                    <Box>
                      <Typography variant="h4" fontWeight={700}>
                        {stats.total_usage_count}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Total Usage
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}

        {/* Filters */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  placeholder="Search templates..."
                  value={search}
                  onChange={handleSearch}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12} md={2}>
                <FormControl fullWidth>
                  <InputLabel>OS Type</InputLabel>
                  <Select
                    value={osTypeFilter}
                    onChange={handleOsTypeFilter}
                    label="OS Type"
                  >
                    <MenuItem value="">All OS</MenuItem>
                    <MenuItem value="linux">Linux</MenuItem>
                    <MenuItem value="windows">Windows</MenuItem>
                    <MenuItem value="macos">macOS</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={2}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={statusFilter}
                    onChange={handleStatusFilter}
                    label="Status"
                  >
                    <MenuItem value="">All Status</MenuItem>
                    <MenuItem value="true">Active</MenuItem>
                    <MenuItem value="false">Inactive</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={2}>
                <FormControl fullWidth>
                  <InputLabel>Visibility</InputLabel>
                  <Select
                    value={visibilityFilter}
                    onChange={handleVisibilityFilter}
                    label="Visibility"
                  >
                    <MenuItem value="">All</MenuItem>
                    <MenuItem value="true">Public</MenuItem>
                    <MenuItem value="false">Private</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={2}>
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  onClick={() => {
                    fetchTemplates();
                    fetchStats();
                  }}
                >
                  Refresh
                </Button>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Error Alert */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Templates Table */}
        <Card>
          <CardContent>
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                <CircularProgress />
              </Box>
            ) : (
              <>
                <TableContainer component={Paper} elevation={0}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Template Details</TableCell>
                        <TableCell>Creator</TableCell>
                        <TableCell>Resources</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Usage</TableCell>
                        <TableCell>Created</TableCell>
                        <TableCell align="right">Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {templates.map((template) => (
                        <TableRow key={template.id}>
                          <TableCell>
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: 2,
                              }}
                            >
                              <Avatar sx={{ bgcolor: 'primary.main' }}>
                                {getOSIcon(template.os_type)}
                              </Avatar>
                              <Box>
                                <Typography
                                  variant="subtitle2"
                                  fontWeight={600}
                                >
                                  {template.name}
                                </Typography>
                                <Typography
                                  variant="body2"
                                  color="textSecondary"
                                >
                                  {template.os_type}{' '}
                                  {template.os_version &&
                                    `- ${template.os_version}`}
                                </Typography>
                                {template.description && (
                                  <Typography
                                    variant="caption"
                                    color="textSecondary"
                                  >
                                    {template.description}
                                  </Typography>
                                )}
                              </Box>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {template.created_by_name || 'Unknown'}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Stack spacing={0.5}>
                              <Box
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 1,
                                }}
                              >
                                <SpeedIcon fontSize="small" />
                                <Typography variant="caption">
                                  {template.default_cpu_cores} CPU
                                </Typography>
                              </Box>
                              <Box
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 1,
                                }}
                              >
                                <MemoryIcon fontSize="small" />
                                <Typography variant="caption">
                                  {Math.round(
                                    template.default_memory_mb / 1024
                                  )}{' '}
                                  GB RAM
                                </Typography>
                              </Box>
                              <Box
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 1,
                                }}
                              >
                                <StorageIcon fontSize="small" />
                                <Typography variant="caption">
                                  {template.default_disk_size} GB Disk
                                </Typography>
                              </Box>
                            </Stack>
                          </TableCell>
                          <TableCell>
                            <Stack spacing={1}>
                              <Chip
                                label={
                                  template.is_active ? 'Active' : 'Inactive'
                                }
                                color={
                                  template.is_active ? 'success' : 'default'
                                }
                                size="small"
                              />
                              <Chip
                                label={template.build_status}
                                color={
                                  getBuildStatusColor(
                                    template.build_status
                                  ) as any
                                }
                                size="small"
                              />
                              <Box
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 0.5,
                                }}
                              >
                                {template.is_public ? (
                                  <PublicIcon fontSize="small" />
                                ) : (
                                  <LockIcon fontSize="small" />
                                )}
                                <Typography variant="caption">
                                  {template.is_public ? 'Public' : 'Private'}
                                </Typography>
                              </Box>
                            </Stack>
                          </TableCell>
                          <TableCell>
                            <Stack spacing={0.5}>
                              <Typography variant="body2">
                                {template.usage_count} VMs created
                              </Typography>
                              <Typography
                                variant="caption"
                                color="textSecondary"
                              >
                                {template.lab_count} labs assigned
                              </Typography>
                            </Stack>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {new Date(
                                template.created_at
                              ).toLocaleDateString()}
                            </Typography>
                          </TableCell>
                          <TableCell align="right">
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <Tooltip title="Edit Template">
                                <IconButton
                                  size="small"
                                  onClick={() => handleEditTemplate(template)}
                                >
                                  <EditIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip
                                title={
                                  template.is_active ? 'Deactivate' : 'Activate'
                                }
                              >
                                <IconButton
                                  size="small"
                                  onClick={() =>
                                    handleToggleTemplateStatus(template)
                                  }
                                  color={
                                    template.is_active ? 'warning' : 'success'
                                  }
                                >
                                  {template.is_active ? (
                                    <StopIcon />
                                  ) : (
                                    <PlayArrowIcon />
                                  )}
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Delete Template">
                                <IconButton
                                  size="small"
                                  onClick={() => handleDeleteTemplate(template)}
                                  color="error"
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </Tooltip>
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>

                {/* Pagination */}
                {totalPages > 1 && (
                  <Box
                    sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}
                  >
                    <Pagination
                      count={totalPages}
                      page={page}
                      onChange={handlePageChange}
                      color="primary"
                    />
                  </Box>
                )}

                {templates.length === 0 && !loading && (
                  <Box sx={{ textAlign: 'center', py: 4 }}>
                    <Typography variant="h6" color="textSecondary">
                      No templates found
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Create your first template to get started
                    </Typography>
                  </Box>
                )}
              </>
            )}
          </CardContent>
        </Card>
      </Box>
    </Container>
  );
};

export default TemplateManagement;
