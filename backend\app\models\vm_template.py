from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, Table, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .base import Base

# Association table for many-to-many relationship between templates and labs
template_labs = Table(
    'template_labs',
    Base.metadata,
    Column('template_id', Integer, ForeignKey('vm_templates.id'), primary_key=True),
    Column('lab_id', Integer, ForeignKey('labs.id'), primary_key=True)
)

class VMTemplate(Base):
    __tablename__ = "vm_templates"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)
    
    # Template creator
    created_by_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Template configuration
    os_type = Column(String(50), nullable=False)  # e.g., 'linux', 'windows'
    os_version = Column(String(100), nullable=True)  # e.g., 'Ubuntu 22.04', 'Windows 11'
    
    # Default resource allocation
    default_cpu_cores = Column(Integer, default=2)
    default_memory_mb = Column(Integer, default=2048)
    default_disk_size = Column(Integer, default=20)  # GB
    
    # Template source
    source_type = Column(String(20), nullable=False, default='iso')  # 'iso', 'existing_vm', 'cloud_image'
    source_path = Column(String(500), nullable=True)  # Path to ISO, VM ID, or cloud image URL
    
    # Template configuration
    template_config = Column(JSON, nullable=True)  # Additional configuration as JSON
    
    # Network configuration
    network_config = Column(JSON, nullable=True)  # Network settings as JSON
    
    # Software and packages
    installed_software = Column(JSON, nullable=True)  # List of pre-installed software
    
    # Template status
    is_active = Column(Boolean, default=True)
    is_public = Column(Boolean, default=False)  # Public templates available to all users
    
    # Template build status
    build_status = Column(String(20), default='draft')  # 'draft', 'building', 'ready', 'failed'
    build_log = Column(Text, nullable=True)
    
    # Usage statistics
    usage_count = Column(Integer, default=0)  # Number of VMs created from this template
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    last_used_at = Column(DateTime, nullable=True)

    # Relationships
    created_by = relationship("User", back_populates="vm_templates")
    labs = relationship("Lab", secondary=template_labs, back_populates="vm_templates")
    virtual_machines = relationship("VirtualMachine", back_populates="template")

class TemplateCategory(Base):
    __tablename__ = "template_categories"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, unique=True, index=True)
    description = Column(Text, nullable=True)
    icon = Column(String(50), nullable=True)  # Icon name for UI
    color = Column(String(7), nullable=True)  # Hex color code
    
    # Category status
    is_active = Column(Boolean, default=True)
    sort_order = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    templates = relationship("TemplateCategoryAssignment", back_populates="category")

class TemplateCategoryAssignment(Base):
    __tablename__ = "template_category_assignments"

    id = Column(Integer, primary_key=True, index=True)
    template_id = Column(Integer, ForeignKey("vm_templates.id"), nullable=False)
    category_id = Column(Integer, ForeignKey("template_categories.id"), nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())

    # Relationships
    template = relationship("VMTemplate")
    category = relationship("TemplateCategory", back_populates="templates")

class TemplateVersion(Base):
    __tablename__ = "template_versions"

    id = Column(Integer, primary_key=True, index=True)
    template_id = Column(Integer, ForeignKey("vm_templates.id"), nullable=False)
    
    # Version information
    version_number = Column(String(20), nullable=False)  # e.g., '1.0.0', '2.1.3'
    version_name = Column(String(100), nullable=True)  # e.g., 'Initial Release', 'Security Update'
    changelog = Column(Text, nullable=True)
    
    # Version configuration (snapshot of template config at this version)
    config_snapshot = Column(JSON, nullable=False)
    
    # Version status
    is_active = Column(Boolean, default=True)
    is_latest = Column(Boolean, default=False)
    
    # Build information
    build_status = Column(String(20), default='draft')  # 'draft', 'building', 'ready', 'failed'
    build_log = Column(Text, nullable=True)
    build_started_at = Column(DateTime, nullable=True)
    build_completed_at = Column(DateTime, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    template = relationship("VMTemplate")

class TemplateUsageLog(Base):
    __tablename__ = "template_usage_logs"

    id = Column(Integer, primary_key=True, index=True)
    template_id = Column(Integer, ForeignKey("vm_templates.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    vm_id = Column(Integer, ForeignKey("virtual_machines.id"), nullable=True)
    lab_id = Column(Integer, ForeignKey("labs.id"), nullable=True)
    
    # Usage details
    action = Column(String(50), nullable=False)  # 'create_vm', 'clone_vm', 'deploy'
    success = Column(Boolean, default=True)
    error_message = Column(Text, nullable=True)
    
    # Performance metrics
    creation_time_seconds = Column(Integer, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())

    # Relationships
    template = relationship("VMTemplate")
    user = relationship("User")
    virtual_machine = relationship("VirtualMachine")
    lab = relationship("Lab")
