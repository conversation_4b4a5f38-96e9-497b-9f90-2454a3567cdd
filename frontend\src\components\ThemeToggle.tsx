import React from 'react';
import {
  ToggleButton,
  ToggleButtonGroup,
  Tooltip,
  Box,
  useTheme as useMuiTheme,
  alpha,
} from '@mui/material';
import { LightMode, DarkMode, SettingsBrightness } from '@mui/icons-material';
import { useTheme } from '../contexts/ThemeContext';

const ThemeToggle: React.FC = () => {
  const { mode, setTheme } = useTheme();
  const muiTheme = useMuiTheme();

  const handleThemeChange = (
    event: React.MouseEvent<HTMLElement>,
    newMode: string | null
  ) => {
    if (newMode !== null) {
      setTheme(newMode as 'light' | 'dark' | 'system');
    }
  };

  return (
    <Tooltip title="Theme Settings">
      <Box>
        <ToggleButtonGroup
          value={mode}
          exclusive
          onChange={handleThemeChange}
          size="small"
          sx={{
            backgroundColor: alpha(muiTheme.palette.background.paper, 0.8),
            border: `1px solid ${alpha(muiTheme.palette.divider, 0.5)}`,
            borderRadius: 2,
            '& .MuiToggleButton-root': {
              border: 'none',
              borderRadius: '6px !important',
              color: muiTheme.palette.text.secondary,
              minWidth: 40,
              height: 36,
              transition: 'all 0.3s ease-in-out',
              '&:hover': {
                backgroundColor: alpha(muiTheme.palette.primary.main, 0.1),
                color: muiTheme.palette.primary.main,
                transform: 'scale(1.05)',
              },
              '&.Mui-selected': {
                backgroundColor: muiTheme.palette.primary.main,
                color: muiTheme.palette.primary.contrastText,
                '&:hover': {
                  backgroundColor: muiTheme.palette.primary.dark,
                },
              },
              '& .MuiSvgIcon-root': {
                fontSize: '1.2rem',
                transition: 'all 0.3s ease-in-out',
              },
            },
          }}
        >
          <ToggleButton value="light" aria-label="light mode">
            <LightMode />
          </ToggleButton>
          <ToggleButton value="dark" aria-label="dark mode">
            <DarkMode />
          </ToggleButton>
          <ToggleButton value="system" aria-label="system mode">
            <SettingsBrightness />
          </ToggleButton>
        </ToggleButtonGroup>
      </Box>
    </Tooltip>
  );
};

export default ThemeToggle;
