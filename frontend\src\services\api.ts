import axios, { AxiosInstance, AxiosError } from 'axios';
import {
  TokenResponse,
  ApiResponse,
  LoginCredentials,
  VMCreateData,
  VMUpdateData,
  VMActionData,
  ProfileUpdateData,
  VMMetricsResponse,
  User,
  VM,
} from '../types';
import { API_ENDPOINTS } from '../constants';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: API_URL,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Add token to requests if available
    this.api.interceptors.request.use((config) => {
      const token = localStorage.getItem('token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    });
  }

  // Generic HTTP methods
  async get<T = any>(url: string): Promise<T> {
    try {
      const response = await this.api.get<T>(url);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async post<T = any>(url: string, data: unknown): Promise<T> {
    try {
      const response = await this.api.post<T>(url, data);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async put<T = any>(url: string, data: unknown): Promise<T> {
    try {
      const response = await this.api.put<T>(url, data);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async delete<T>(url: string): Promise<T> {
    try {
      const response = await this.api.delete<T>(url);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Auth endpoints
  async login(credentials: LoginCredentials): Promise<TokenResponse> {
    return this.post<TokenResponse>('/auth/login', credentials);
  }

  async register(data: {
    username: string;
    email: string;
    password: string;
    full_name?: string;
  }): Promise<User> {
    return this.post('/auth/register', data);
  }

  async getCurrentUser() {
    return this.get<User>('/auth/me');
  }

  async updateProfile(data: ProfileUpdateData) {
    return this.put<User>('/auth/me', data);
  }

  // VM endpoints
  async getVMs() {
    return this.get<VM[]>('/vm/');
  }

  async createVM(data: VMCreateData) {
    return this.post<VM>('/vm/', data);
  }

  async updateVM(vmId: number, data: VMUpdateData) {
    return this.put<VM>(`/vm/${vmId}`, data);
  }

  async performVMAction(vmId: number, data: VMActionData) {
    return this.post<VM>(`/vm/${vmId}/action`, data);
  }

  async deleteVM(vmId: number) {
    return this.delete<void>(`/vm/${vmId}`);
  }

  // VM Metrics
  async getVMMetrics(vmId: number): Promise<VMMetricsResponse> {
    return this.get<VMMetricsResponse>(`/vm/${vmId}/metrics`);
  }

  // System endpoints
  async getSystemMetrics() {
    return this.get(API_ENDPOINTS.SYSTEM.METRICS);
  }

  async getHistoricalMetrics(hours: number = 24) {
    return this.get(`${API_ENDPOINTS.SYSTEM.HISTORICAL_METRICS}?hours=${hours}`);
  }

  async getSystemHealth() {
    return this.get(API_ENDPOINTS.SYSTEM.HEALTH);
  }

  async getClusterStatus() {
    return this.get(API_ENDPOINTS.SYSTEM.CLUSTER_STATUS);
  }

  async getNodes() {
    return this.get(API_ENDPOINTS.SYSTEM.NODES);
  }

  // Admin API methods
  async getUsers(params?: {
    page?: number;
    per_page?: number;
    search?: string;
    role?: string;
    is_active?: boolean;
  }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== '') {
          queryParams.append(key, value.toString());
        }
      });
    }
    return this.get(`/admin/users?${queryParams.toString()}`);
  }

  async getUserStats() {
    return this.get('/admin/users/stats');
  }

  async createUser(userData: {
    username: string;
    email: string;
    full_name?: string;
    password: string;
    role: string;
    is_active: boolean;
  }) {
    return this.post('/admin/users', userData);
  }

  async updateUser(userId: number, userData: {
    username?: string;
    email?: string;
    full_name?: string;
    password?: string;
    role?: string;
    is_active?: boolean;
  }) {
    return this.put(`/admin/users/${userId}`, userData);
  }

  async deleteUser(userId: number) {
    return this.delete(`/admin/users/${userId}`);
  }

  async activateUser(userId: number) {
    return this.post(`/admin/users/${userId}/activate`, {});
  }

  async deactivateUser(userId: number) {
    return this.post(`/admin/users/${userId}/deactivate`, {});
  }

  async resetUserPassword(userId: number, newPassword: string) {
    return this.post(`/admin/users/${userId}/reset-password`, { new_password: newPassword });
  }

  // Lab API methods
  async getLabs(params?: {
    page?: number;
    per_page?: number;
    search?: string;
    instructor_id?: number;
    is_active?: boolean;
  }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== '') {
          queryParams.append(key, value.toString());
        }
      });
    }
    return this.get(`/labs?${queryParams.toString()}`);
  }

  async getLabStats() {
    return this.get('/labs/stats');
  }

  async createLab(labData: {
    name: string;
    description?: string;
    max_students?: number;
    max_vms_per_student?: number;
    is_active?: boolean;
    start_date?: string;
    end_date?: string;
    instructor_id?: number;
  }) {
    return this.post('/labs', labData);
  }

  async getLab(labId: number) {
    return this.get(`/labs/${labId}`);
  }

  async updateLab(labId: number, labData: {
    name?: string;
    description?: string;
    max_students?: number;
    max_vms_per_student?: number;
    is_active?: boolean;
    start_date?: string;
    end_date?: string;
  }) {
    return this.put(`/labs/${labId}`, labData);
  }

  async deleteLab(labId: number) {
    return this.delete(`/labs/${labId}`);
  }

  async assignStudentsToLab(labId: number, studentIds: number[]) {
    return this.post(`/labs/${labId}/assign-students`, { lab_id: labId, student_ids: studentIds });
  }

  async assignVMsToLab(labId: number, vmIds: number[]) {
    return this.post(`/labs/${labId}/assign-vms`, { lab_id: labId, vm_ids: vmIds });
  }

  // Template API methods
  async getTemplates(params?: {
    page?: number;
    per_page?: number;
    search?: string;
    category_id?: number;
    os_type?: string;
    is_active?: boolean;
    is_public?: boolean;
    created_by_id?: number;
  }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== '') {
          queryParams.append(key, value.toString());
        }
      });
    }
    return this.get(`/templates?${queryParams.toString()}`);
  }

  async getTemplateStats() {
    return this.get('/templates/stats');
  }

  async createTemplate(templateData: {
    name: string;
    description?: string;
    os_type: string;
    os_version?: string;
    default_cpu_cores?: number;
    default_memory_mb?: number;
    default_disk_size?: number;
    source_type?: string;
    source_path?: string;
    template_config?: any;
    network_config?: any;
    installed_software?: string[];
    is_active?: boolean;
    is_public?: boolean;
    created_by_id?: number;
  }) {
    return this.post('/templates', templateData);
  }

  async getTemplate(templateId: number) {
    return this.get(`/templates/${templateId}`);
  }

  async updateTemplate(templateId: number, templateData: {
    name?: string;
    description?: string;
    os_type?: string;
    os_version?: string;
    default_cpu_cores?: number;
    default_memory_mb?: number;
    default_disk_size?: number;
    source_type?: string;
    source_path?: string;
    template_config?: any;
    network_config?: any;
    installed_software?: string[];
    is_active?: boolean;
    is_public?: boolean;
  }) {
    return this.put(`/templates/${templateId}`, templateData);
  }

  async deleteTemplate(templateId: number) {
    return this.delete(`/templates/${templateId}`);
  }

  // Error handling
  private handleError(error: unknown) {
    if (error instanceof AxiosError) {
      if (error.response?.status === 401) {
        // Clear token on authentication error
        localStorage.removeItem('token');
      }

      const message = error.response?.data?.detail || error.message;
      throw new Error(message);
    }
    throw error;
  }
}

const api = new ApiService();
export default api;
