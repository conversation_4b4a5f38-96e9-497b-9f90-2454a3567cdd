from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, and_, or_, desc
from typing import List, Optional
from datetime import datetime
import logging
import uuid

from ..models.user import User, UserRole
from ..models.vm_template import VMTemplate, TemplateCategory, TemplateCategoryAssignment, TemplateUsageLog
from ..models.lab import Lab
from ..models.virtual_machine import VirtualMachine
from ..database import get_db
from ..routers.auth import get_current_user
from ..schemas.vm_template import (
    VMTemplateCreate, VMTemplateUpdate, VMTemplateResponse, VMTemplateListResponse,
    TemplateCategoryCreate, TemplateCategoryUpdate, TemplateCategoryResponse,
    TemplateStatsResponse, AssignTemplatesToLabRequest, CreateVMFromTemplateRequest,
    BulkTemplateResponse, TemplateBuildRequest, TemplateBuildResponse
)

router = APIRouter(prefix="/templates", tags=["templates"])
logger = logging.getLogger(__name__)

def require_instructor_or_admin(current_user: User = Depends(get_current_user)) -> User:
    """Dependency to ensure only instructors or admins can manage templates."""
    if current_user.role not in [UserRole.TEACHER, UserRole.ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Instructor or admin access required"
        )
    return current_user

def require_admin(current_user: User = Depends(get_current_user)) -> User:
    """Dependency to ensure only admins can access admin-only endpoints."""
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    return current_user

# Template Management Endpoints

@router.get("/", response_model=VMTemplateListResponse)
async def list_templates(
    page: int = Query(1, ge=1),
    per_page: int = Query(10, ge=1, le=100),
    search: Optional[str] = Query(None),
    category_id: Optional[int] = Query(None),
    os_type: Optional[str] = Query(None),
    is_active: Optional[bool] = Query(None),
    is_public: Optional[bool] = Query(None),
    created_by_id: Optional[int] = Query(None),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """List VM templates with pagination and filtering."""
    logger.info(f"User {current_user.username} listing templates")
    
    # Build query
    query = db.query(VMTemplate).options(
        joinedload(VMTemplate.created_by),
        joinedload(VMTemplate.labs)
    )
    
    # Filter by creator for non-admin users (they can see their own + public templates)
    if current_user.role != UserRole.ADMIN:
        query = query.filter(
            or_(
                VMTemplate.created_by_id == current_user.id,
                VMTemplate.is_public == True
            )
        )
    elif created_by_id and current_user.role == UserRole.ADMIN:
        query = query.filter(VMTemplate.created_by_id == created_by_id)
    
    # Apply filters
    if search:
        search_filter = or_(
            VMTemplate.name.ilike(f"%{search}%"),
            VMTemplate.description.ilike(f"%{search}%"),
            VMTemplate.os_type.ilike(f"%{search}%"),
            VMTemplate.os_version.ilike(f"%{search}%")
        )
        query = query.filter(search_filter)
    
    if category_id:
        query = query.join(TemplateCategoryAssignment).filter(
            TemplateCategoryAssignment.category_id == category_id
        )
    
    if os_type:
        query = query.filter(VMTemplate.os_type.ilike(f"%{os_type}%"))
    
    if is_active is not None:
        query = query.filter(VMTemplate.is_active == is_active)
    
    if is_public is not None:
        query = query.filter(VMTemplate.is_public == is_public)
    
    # Apply sorting (most used first, then newest)
    query = query.order_by(desc(VMTemplate.usage_count), desc(VMTemplate.created_at))
    
    # Get total count
    total = query.count()
    
    # Apply pagination
    offset = (page - 1) * per_page
    templates = query.offset(offset).limit(per_page).all()
    
    # Build response
    template_responses = []
    for template in templates:
        # Get categories for this template
        categories = db.query(TemplateCategory.name).join(TemplateCategoryAssignment).filter(
            TemplateCategoryAssignment.template_id == template.id
        ).all()
        category_names = [cat[0] for cat in categories]
        
        template_dict = {
            "id": template.id,
            "name": template.name,
            "description": template.description,
            "created_by_id": template.created_by_id,
            "created_by_name": template.created_by.username if template.created_by else None,
            "os_type": template.os_type,
            "os_version": template.os_version,
            "default_cpu_cores": template.default_cpu_cores,
            "default_memory_mb": template.default_memory_mb,
            "default_disk_size": template.default_disk_size,
            "source_type": template.source_type,
            "source_path": template.source_path,
            "template_config": template.template_config,
            "network_config": template.network_config,
            "installed_software": template.installed_software,
            "is_active": template.is_active,
            "is_public": template.is_public,
            "build_status": template.build_status,
            "build_log": template.build_log,
            "usage_count": template.usage_count,
            "created_at": template.created_at,
            "updated_at": template.updated_at,
            "last_used_at": template.last_used_at,
            "categories": category_names,
            "lab_count": len(template.labs)
        }
        template_responses.append(VMTemplateResponse(**template_dict))
    
    total_pages = (total + per_page - 1) // per_page
    
    return VMTemplateListResponse(
        templates=template_responses,
        total=total,
        page=page,
        per_page=per_page,
        total_pages=total_pages
    )

@router.get("/stats", response_model=TemplateStatsResponse)
async def get_template_stats(
    current_user: User = Depends(require_instructor_or_admin),
    db: Session = Depends(get_db)
):
    """Get template statistics."""
    logger.info(f"User {current_user.username} requesting template stats")
    
    # Filter by creator for non-admin users
    template_filter = VMTemplate.created_by_id == current_user.id if current_user.role == UserRole.TEACHER else True
    
    # Get template counts
    total_templates = db.query(VMTemplate).filter(template_filter).count()
    active_templates = db.query(VMTemplate).filter(and_(template_filter, VMTemplate.is_active == True)).count()
    inactive_templates = total_templates - active_templates
    public_templates = db.query(VMTemplate).filter(and_(template_filter, VMTemplate.is_public == True)).count()
    private_templates = total_templates - public_templates
    
    # Get category count
    total_categories = db.query(TemplateCategory).filter(TemplateCategory.is_active == True).count()
    
    # Get total usage count
    total_usage_count = db.query(func.sum(VMTemplate.usage_count)).filter(template_filter).scalar() or 0
    
    # Get most used templates
    most_used_query = db.query(VMTemplate).filter(template_filter).order_by(desc(VMTemplate.usage_count)).limit(5)
    most_used_templates = []
    for template in most_used_query:
        most_used_templates.append({
            "id": template.id,
            "name": template.name,
            "usage_count": template.usage_count,
            "os_type": template.os_type
        })
    
    return TemplateStatsResponse(
        total_templates=total_templates,
        active_templates=active_templates,
        inactive_templates=inactive_templates,
        public_templates=public_templates,
        private_templates=private_templates,
        total_categories=total_categories,
        total_usage_count=total_usage_count,
        most_used_templates=most_used_templates
    )

@router.post("/", response_model=VMTemplateResponse)
async def create_template(
    template_data: VMTemplateCreate,
    current_user: User = Depends(require_instructor_or_admin),
    db: Session = Depends(get_db)
):
    """Create a new VM template."""
    logger.info(f"User {current_user.username} creating template: {template_data.name}")
    
    # Set creator_id if not provided
    created_by_id = template_data.created_by_id or current_user.id
    
    # Verify creator exists and user has permission
    if current_user.role == UserRole.TEACHER and created_by_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Teachers can only create templates for themselves"
        )
    
    if current_user.role == UserRole.ADMIN and template_data.created_by_id:
        creator = db.query(User).filter(User.id == created_by_id).first()
        if not creator or creator.role not in [UserRole.TEACHER, UserRole.ADMIN]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid creator ID"
            )
    
    try:
        # Create new template
        db_template = VMTemplate(
            name=template_data.name,
            description=template_data.description,
            created_by_id=created_by_id,
            os_type=template_data.os_type,
            os_version=template_data.os_version,
            default_cpu_cores=template_data.default_cpu_cores,
            default_memory_mb=template_data.default_memory_mb,
            default_disk_size=template_data.default_disk_size,
            source_type=template_data.source_type,
            source_path=template_data.source_path,
            template_config=template_data.template_config,
            network_config=template_data.network_config,
            installed_software=template_data.installed_software,
            is_active=template_data.is_active,
            is_public=template_data.is_public,
            build_status='draft'
        )
        
        db.add(db_template)
        db.commit()
        db.refresh(db_template)
        
        # Load creator relationship
        db_template = db.query(VMTemplate).options(joinedload(VMTemplate.created_by)).filter(
            VMTemplate.id == db_template.id
        ).first()
        
        logger.info(f"Template created successfully: {template_data.name}")
        
        template_dict = {
            "id": db_template.id,
            "name": db_template.name,
            "description": db_template.description,
            "created_by_id": db_template.created_by_id,
            "created_by_name": db_template.created_by.username if db_template.created_by else None,
            "os_type": db_template.os_type,
            "os_version": db_template.os_version,
            "default_cpu_cores": db_template.default_cpu_cores,
            "default_memory_mb": db_template.default_memory_mb,
            "default_disk_size": db_template.default_disk_size,
            "source_type": db_template.source_type,
            "source_path": db_template.source_path,
            "template_config": db_template.template_config,
            "network_config": db_template.network_config,
            "installed_software": db_template.installed_software,
            "is_active": db_template.is_active,
            "is_public": db_template.is_public,
            "build_status": db_template.build_status,
            "build_log": db_template.build_log,
            "usage_count": db_template.usage_count,
            "created_at": db_template.created_at,
            "updated_at": db_template.updated_at,
            "last_used_at": db_template.last_used_at,
            "categories": [],
            "lab_count": 0
        }
        return VMTemplateResponse(**template_dict)
        
    except Exception as e:
        logger.error(f"Failed to create template {template_data.name}: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create template"
        )

@router.get("/{template_id}", response_model=VMTemplateResponse)
async def get_template(
    template_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get template details by ID."""
    template = db.query(VMTemplate).options(
        joinedload(VMTemplate.created_by),
        joinedload(VMTemplate.labs)
    ).filter(VMTemplate.id == template_id).first()

    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Template not found"
        )

    # Check permissions (can view own templates, public templates, or if admin)
    if (current_user.role != UserRole.ADMIN and
        template.created_by_id != current_user.id and
        not template.is_public):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )

    # Get categories for this template
    categories = db.query(TemplateCategory.name).join(TemplateCategoryAssignment).filter(
        TemplateCategoryAssignment.template_id == template.id
    ).all()
    category_names = [cat[0] for cat in categories]

    template_dict = {
        "id": template.id,
        "name": template.name,
        "description": template.description,
        "created_by_id": template.created_by_id,
        "created_by_name": template.created_by.username if template.created_by else None,
        "os_type": template.os_type,
        "os_version": template.os_version,
        "default_cpu_cores": template.default_cpu_cores,
        "default_memory_mb": template.default_memory_mb,
        "default_disk_size": template.default_disk_size,
        "source_type": template.source_type,
        "source_path": template.source_path,
        "template_config": template.template_config,
        "network_config": template.network_config,
        "installed_software": template.installed_software,
        "is_active": template.is_active,
        "is_public": template.is_public,
        "build_status": template.build_status,
        "build_log": template.build_log,
        "usage_count": template.usage_count,
        "created_at": template.created_at,
        "updated_at": template.updated_at,
        "last_used_at": template.last_used_at,
        "categories": category_names,
        "lab_count": len(template.labs)
    }
    return VMTemplateResponse(**template_dict)

@router.put("/{template_id}", response_model=VMTemplateResponse)
async def update_template(
    template_id: int,
    template_data: VMTemplateUpdate,
    current_user: User = Depends(require_instructor_or_admin),
    db: Session = Depends(get_db)
):
    """Update template details."""
    logger.info(f"User {current_user.username} updating template ID: {template_id}")

    template = db.query(VMTemplate).filter(VMTemplate.id == template_id).first()
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Template not found"
        )

    # Check permissions
    if current_user.role == UserRole.TEACHER and template.created_by_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only update your own templates"
        )

    try:
        # Update fields if provided
        if template_data.name is not None:
            template.name = template_data.name
        if template_data.description is not None:
            template.description = template_data.description
        if template_data.os_type is not None:
            template.os_type = template_data.os_type
        if template_data.os_version is not None:
            template.os_version = template_data.os_version
        if template_data.default_cpu_cores is not None:
            template.default_cpu_cores = template_data.default_cpu_cores
        if template_data.default_memory_mb is not None:
            template.default_memory_mb = template_data.default_memory_mb
        if template_data.default_disk_size is not None:
            template.default_disk_size = template_data.default_disk_size
        if template_data.source_type is not None:
            template.source_type = template_data.source_type
        if template_data.source_path is not None:
            template.source_path = template_data.source_path
        if template_data.template_config is not None:
            template.template_config = template_data.template_config
        if template_data.network_config is not None:
            template.network_config = template_data.network_config
        if template_data.installed_software is not None:
            template.installed_software = template_data.installed_software
        if template_data.is_active is not None:
            template.is_active = template_data.is_active
        if template_data.is_public is not None:
            template.is_public = template_data.is_public

        db.commit()
        db.refresh(template)

        # Load relationships
        template = db.query(VMTemplate).options(
            joinedload(VMTemplate.created_by),
            joinedload(VMTemplate.labs)
        ).filter(VMTemplate.id == template_id).first()

        logger.info(f"Template updated successfully: {template.name}")

        # Get categories
        categories = db.query(TemplateCategory.name).join(TemplateCategoryAssignment).filter(
            TemplateCategoryAssignment.template_id == template.id
        ).all()
        category_names = [cat[0] for cat in categories]

        template_dict = {
            "id": template.id,
            "name": template.name,
            "description": template.description,
            "created_by_id": template.created_by_id,
            "created_by_name": template.created_by.username if template.created_by else None,
            "os_type": template.os_type,
            "os_version": template.os_version,
            "default_cpu_cores": template.default_cpu_cores,
            "default_memory_mb": template.default_memory_mb,
            "default_disk_size": template.default_disk_size,
            "source_type": template.source_type,
            "source_path": template.source_path,
            "template_config": template.template_config,
            "network_config": template.network_config,
            "installed_software": template.installed_software,
            "is_active": template.is_active,
            "is_public": template.is_public,
            "build_status": template.build_status,
            "build_log": template.build_log,
            "usage_count": template.usage_count,
            "created_at": template.created_at,
            "updated_at": template.updated_at,
            "last_used_at": template.last_used_at,
            "categories": category_names,
            "lab_count": len(template.labs)
        }
        return VMTemplateResponse(**template_dict)

    except Exception as e:
        logger.error(f"Failed to update template {template_id}: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update template"
        )

@router.delete("/{template_id}")
async def delete_template(
    template_id: int,
    current_user: User = Depends(require_instructor_or_admin),
    db: Session = Depends(get_db)
):
    """Delete a template."""
    logger.info(f"User {current_user.username} deleting template ID: {template_id}")

    template = db.query(VMTemplate).filter(VMTemplate.id == template_id).first()
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Template not found"
        )

    # Check permissions
    if current_user.role == UserRole.TEACHER and template.created_by_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only delete your own templates"
        )

    try:
        # Check if template is being used by VMs
        vm_count = db.query(VirtualMachine).filter(VirtualMachine.template_id == template_id).count()
        if vm_count > 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot delete template with {vm_count} virtual machines. Please remove VMs first."
            )

        db.delete(template)
        db.commit()

        logger.info(f"Template deleted successfully: {template.name}")
        return {"message": "Template deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete template {template_id}: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete template"
        )
