import React from 'react';
import { BrowserRouter as Router, Route, Routes } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from 'react-query';

import { ThemeProvider } from './contexts/ThemeContext';
import Layout from './components/Layout';
import ProtectedRoute from './components/ProtectedRoute';
import { NotificationProvider } from './components/Notifications';

// Pages
import Login from './pages/Login';
import Register from './pages/Register';
import Dashboard from './pages/Dashboard';
import VirtualMachines from './pages/VirtualMachines';
import Profile from './pages/Profile';
import UserManagement from './pages/admin/UserManagement';

// Initialize React Query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

// Configure future flags for React Router v7
const router = {
  future: {
    v7_relativeSplatPath: true,
    v7_startTransition: true,
  },
};

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <NotificationProvider>
        <Router {...router}>
          <Routes>
            {/* Public routes */}
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />

            {/* Protected routes */}
            <Route element={<ProtectedRoute />}>
              <Route element={<Layout />}>
                <Route index element={<Dashboard />} />
                <Route path="vms/*" element={<VirtualMachines />} />
                <Route path="profile" element={<Profile />} />

                {/* Admin routes */}
                <Route path="admin/users" element={<UserManagement />} />

                {/* Catch-all redirect to dashboard */}
                <Route path="*" element={<Dashboard />} />
              </Route>
            </Route>
          </Routes>
        </Router>
        </NotificationProvider>
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export default App;
