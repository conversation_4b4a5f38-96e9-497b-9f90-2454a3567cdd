"""Add VM template management models

Revision ID: aadde34a51f8
Revises: 6385eec34c52
Create Date: 2025-07-08 15:45:25.664472

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'aadde34a51f8'
down_revision = '6385eec34c52'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('template_categories',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('icon', sa.String(length=50), nullable=True),
    sa.Column('color', sa.String(length=7), nullable=True),
    sa.Column('is_active', sa.<PERSON>(), nullable=True),
    sa.Column('sort_order', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_template_categories_id'), 'template_categories', ['id'], unique=False)
    op.create_index(op.f('ix_template_categories_name'), 'template_categories', ['name'], unique=True)
    op.create_table('vm_templates',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('created_by_id', sa.Integer(), nullable=False),
    sa.Column('os_type', sa.String(length=50), nullable=False),
    sa.Column('os_version', sa.String(length=100), nullable=True),
    sa.Column('default_cpu_cores', sa.Integer(), nullable=True),
    sa.Column('default_memory_mb', sa.Integer(), nullable=True),
    sa.Column('default_disk_size', sa.Integer(), nullable=True),
    sa.Column('source_type', sa.String(length=20), nullable=False),
    sa.Column('source_path', sa.String(length=500), nullable=True),
    sa.Column('template_config', sa.JSON(), nullable=True),
    sa.Column('network_config', sa.JSON(), nullable=True),
    sa.Column('installed_software', sa.JSON(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_public', sa.Boolean(), nullable=True),
    sa.Column('build_status', sa.String(length=20), nullable=True),
    sa.Column('build_log', sa.Text(), nullable=True),
    sa.Column('usage_count', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('last_used_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['created_by_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_vm_templates_id'), 'vm_templates', ['id'], unique=False)
    op.create_index(op.f('ix_vm_templates_name'), 'vm_templates', ['name'], unique=False)
    op.create_table('template_category_assignments',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('template_id', sa.Integer(), nullable=False),
    sa.Column('category_id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['category_id'], ['template_categories.id'], ),
    sa.ForeignKeyConstraint(['template_id'], ['vm_templates.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_template_category_assignments_id'), 'template_category_assignments', ['id'], unique=False)
    op.create_table('template_labs',
    sa.Column('template_id', sa.Integer(), nullable=False),
    sa.Column('lab_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['lab_id'], ['labs.id'], ),
    sa.ForeignKeyConstraint(['template_id'], ['vm_templates.id'], ),
    sa.PrimaryKeyConstraint('template_id', 'lab_id')
    )
    op.create_table('template_versions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('template_id', sa.Integer(), nullable=False),
    sa.Column('version_number', sa.String(length=20), nullable=False),
    sa.Column('version_name', sa.String(length=100), nullable=True),
    sa.Column('changelog', sa.Text(), nullable=True),
    sa.Column('config_snapshot', sa.JSON(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_latest', sa.Boolean(), nullable=True),
    sa.Column('build_status', sa.String(length=20), nullable=True),
    sa.Column('build_log', sa.Text(), nullable=True),
    sa.Column('build_started_at', sa.DateTime(), nullable=True),
    sa.Column('build_completed_at', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['template_id'], ['vm_templates.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_template_versions_id'), 'template_versions', ['id'], unique=False)
    op.create_table('template_usage_logs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('template_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('vm_id', sa.Integer(), nullable=True),
    sa.Column('lab_id', sa.Integer(), nullable=True),
    sa.Column('action', sa.String(length=50), nullable=False),
    sa.Column('success', sa.Boolean(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('creation_time_seconds', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['lab_id'], ['labs.id'], ),
    sa.ForeignKeyConstraint(['template_id'], ['vm_templates.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['vm_id'], ['virtual_machines.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_template_usage_logs_id'), 'template_usage_logs', ['id'], unique=False)
    op.add_column('virtual_machines', sa.Column('template_id', sa.Integer(), nullable=True))
    op.drop_constraint(op.f('virtual_machines_ibfk_1'), 'virtual_machines', type_='foreignkey')
    op.drop_index(op.f('ix_virtual_machines_owner_id'), table_name='virtual_machines')
    op.create_index(op.f('ix_virtual_machines_id'), 'virtual_machines', ['id'], unique=False)
    op.create_foreign_key(None, 'virtual_machines', 'vm_templates', ['template_id'], ['id'])
    op.create_foreign_key(None, 'virtual_machines', 'users', ['owner_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'virtual_machines', type_='foreignkey')
    op.drop_constraint(None, 'virtual_machines', type_='foreignkey')
    op.create_foreign_key(op.f('virtual_machines_ibfk_1'), 'virtual_machines', 'users', ['owner_id'], ['id'], ondelete='CASCADE')
    op.drop_index(op.f('ix_virtual_machines_id'), table_name='virtual_machines')
    op.create_index(op.f('ix_virtual_machines_owner_id'), 'virtual_machines', ['owner_id'], unique=False)
    op.drop_column('virtual_machines', 'template_id')
    op.drop_index(op.f('ix_template_usage_logs_id'), table_name='template_usage_logs')
    op.drop_table('template_usage_logs')
    op.drop_index(op.f('ix_template_versions_id'), table_name='template_versions')
    op.drop_table('template_versions')
    op.drop_table('template_labs')
    op.drop_index(op.f('ix_template_category_assignments_id'), table_name='template_category_assignments')
    op.drop_table('template_category_assignments')
    op.drop_index(op.f('ix_vm_templates_name'), table_name='vm_templates')
    op.drop_index(op.f('ix_vm_templates_id'), table_name='vm_templates')
    op.drop_table('vm_templates')
    op.drop_index(op.f('ix_template_categories_name'), table_name='template_categories')
    op.drop_index(op.f('ix_template_categories_id'), table_name='template_categories')
    op.drop_table('template_categories')
    # ### end Alembic commands ###
