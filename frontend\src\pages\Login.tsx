import React, { useState } from 'react';
import {
  Box,
  <PERSON><PERSON>,
  Container,
  TextField,
  Typography,
  Paper,
  Grid,
  Alert,
  CircularProgress,
  Link,
} from '@mui/material';
import { useNavigate, Link as RouterLink } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { login } from '../store/slices/authSlice';
import { useForm } from '../hooks/useForm';
import { useNotification } from '../components/Notifications';

interface LoginForm {
  username: string;
  password: string;
}

const Login = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { showError, showSuccess } = useNotification();
  const [isLoading, setIsLoading] = useState(false);
  const [loginError, setLoginError] = useState<string | null>(null);

  const formConfig = {
    username: {
      initialValue: '',
      validation: [{ required: true, message: 'Username is required' }],
    },
    password: {
      initialValue: '',
      validation: [{ required: true, message: 'Password is required' }],
    },
  };

  const { values, errors, touched, handleChange, handleBlur, handleSubmit } =
    useForm(formConfig);

  const handleLogin = async () => {
    try {
      setIsLoading(true);
      setLoginError(null);

      await dispatch(login(values) as any).unwrap();

      showSuccess('Login successful! Welcome back.');
      navigate('/');
    } catch (error: any) {
      console.error('Login failed:', error);
      const errorMessage =
        error?.message || 'Login failed. Please check your credentials.';
      setLoginError(errorMessage);
      showError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Container component="main" maxWidth="xs">
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Paper elevation={3} sx={{ p: 4, width: '100%' }}>
          <Typography
            component="h1"
            variant="h4"
            align="center"
            gutterBottom
            color="primary"
          >
            Proxmox Lab Manager
          </Typography>
          <Typography
            component="h2"
            variant="h6"
            align="center"
            gutterBottom
            sx={{ mb: 3 }}
          >
            Sign In to Your Account
          </Typography>

          {loginError && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {loginError}
            </Alert>
          )}

          <form onSubmit={handleSubmit(handleLogin)}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  id="username"
                  name="username"
                  label="Username"
                  value={values.username}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.username && !!errors.username}
                  helperText={touched.username && errors.username}
                  disabled={isLoading}
                  autoComplete="username"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  id="password"
                  name="password"
                  label="Password"
                  type="password"
                  value={values.password}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.password && !!errors.password}
                  helperText={touched.password && errors.password}
                  disabled={isLoading}
                  autoComplete="current-password"
                />
              </Grid>
              <Grid item xs={12}>
                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  color="primary"
                  disabled={isLoading}
                  sx={{ mt: 2, mb: 2, py: 1.5 }}
                  startIcon={
                    isLoading ? (
                      <CircularProgress size={20} color="inherit" />
                    ) : null
                  }
                >
                  {isLoading ? 'Signing In...' : 'Sign In'}
                </Button>
              </Grid>
              <Grid item xs={12}>
                <Box sx={{ textAlign: 'center', mt: 2 }}>
                  <Typography variant="body2">
                    Don't have an account?{' '}
                    <Link component={RouterLink} to="/register" color="primary">
                      Register here
                    </Link>
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </form>
        </Paper>
      </Box>
    </Container>
  );
};

export default Login;
