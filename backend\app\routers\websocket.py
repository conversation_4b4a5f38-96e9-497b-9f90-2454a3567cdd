from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, and_, or_, desc
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import logging

from ..models.user import User, UserRole
from ..models.notification import Notification, SystemAlert, NotificationPreference, ActivityLog, RealTimeMetric
from ..database import get_db
from ..routers.auth import get_current_user
from ..websocket.manager import websocket_manager, NotificationType

router = APIRouter(prefix="/websocket", tags=["websocket"])
logger = logging.getLogger(__name__)

# Notification Endpoints

@router.get("/notifications", response_model=List[Dict[str, Any]])
async def get_user_notifications(
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    unread_only: bool = Query(False),
    category: Optional[str] = Query(None),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user notifications with pagination."""
    logger.info(f"User {current_user.username} requesting notifications")
    
    query = db.query(Notification).filter(Notification.user_id == current_user.id)
    
    if unread_only:
        query = query.filter(Notification.is_read == False)
    
    if category:
        query = query.filter(Notification.category == category)
    
    # Filter out expired notifications
    query = query.filter(
        or_(
            Notification.expires_at.is_(None),
            Notification.expires_at > datetime.utcnow()
        )
    )
    
    # Apply pagination
    total = query.count()
    offset = (page - 1) * per_page
    notifications = query.order_by(desc(Notification.created_at)).offset(offset).limit(per_page).all()
    
    notification_list = []
    for notification in notifications:
        notification_list.append({
            "id": notification.id,
            "title": notification.title,
            "message": notification.message,
            "notification_type": notification.notification_type,
            "priority": notification.priority,
            "category": notification.category,
            "is_read": notification.is_read,
            "is_dismissed": notification.is_dismissed,
            "related_entity_type": notification.related_entity_type,
            "related_entity_id": notification.related_entity_id,
            "data": notification.data,
            "action_url": notification.action_url,
            "action_text": notification.action_text,
            "created_at": notification.created_at,
            "read_at": notification.read_at,
            "dismissed_at": notification.dismissed_at
        })
    
    return notification_list

@router.post("/notifications/{notification_id}/read")
async def mark_notification_read(
    notification_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Mark a notification as read."""
    notification = db.query(Notification).filter(
        Notification.id == notification_id,
        Notification.user_id == current_user.id
    ).first()
    
    if not notification:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Notification not found"
        )
    
    if not notification.is_read:
        notification.is_read = True
        notification.read_at = datetime.utcnow()
        db.commit()
    
    return {"message": "Notification marked as read"}

@router.post("/notifications/mark-all-read")
async def mark_all_notifications_read(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Mark all notifications as read for the current user."""
    db.query(Notification).filter(
        Notification.user_id == current_user.id,
        Notification.is_read == False
    ).update({
        "is_read": True,
        "read_at": datetime.utcnow()
    })
    
    db.commit()
    
    return {"message": "All notifications marked as read"}

@router.delete("/notifications/{notification_id}")
async def dismiss_notification(
    notification_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Dismiss a notification."""
    notification = db.query(Notification).filter(
        Notification.id == notification_id,
        Notification.user_id == current_user.id
    ).first()
    
    if not notification:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Notification not found"
        )
    
    notification.is_dismissed = True
    notification.dismissed_at = datetime.utcnow()
    db.commit()
    
    return {"message": "Notification dismissed"}

@router.get("/notifications/stats")
async def get_notification_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get notification statistics for the current user."""
    total_notifications = db.query(Notification).filter(
        Notification.user_id == current_user.id,
        Notification.is_dismissed == False
    ).count()
    
    unread_notifications = db.query(Notification).filter(
        Notification.user_id == current_user.id,
        Notification.is_read == False,
        Notification.is_dismissed == False
    ).count()
    
    high_priority_notifications = db.query(Notification).filter(
        Notification.user_id == current_user.id,
        Notification.priority.in_(['high', 'urgent']),
        Notification.is_read == False,
        Notification.is_dismissed == False
    ).count()
    
    return {
        "total_notifications": total_notifications,
        "unread_notifications": unread_notifications,
        "high_priority_notifications": high_priority_notifications
    }

# System Alert Endpoints

@router.get("/system-alerts", response_model=List[Dict[str, Any]])
async def get_system_alerts(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get active system alerts for the current user."""
    now = datetime.utcnow()
    
    query = db.query(SystemAlert).filter(
        SystemAlert.status == 'active',
        or_(
            SystemAlert.start_time.is_(None),
            SystemAlert.start_time <= now
        ),
        or_(
            SystemAlert.end_time.is_(None),
            SystemAlert.end_time > now
        )
    )
    
    alerts = query.order_by(desc(SystemAlert.created_at)).all()
    
    # Filter alerts based on targeting
    filtered_alerts = []
    for alert in alerts:
        # Check if alert targets this user's role
        if alert.target_roles and current_user.role.value not in alert.target_roles:
            continue
        
        # Check if alert targets this specific user
        if alert.target_users and current_user.id not in alert.target_users:
            continue
        
        filtered_alerts.append({
            "id": alert.id,
            "title": alert.title,
            "message": alert.message,
            "alert_type": alert.alert_type,
            "severity": alert.severity,
            "is_dismissible": alert.is_dismissible,
            "show_on_dashboard": alert.show_on_dashboard,
            "show_as_banner": alert.show_as_banner,
            "data": alert.data,
            "created_at": alert.created_at
        })
    
    return filtered_alerts

# Real-time Metrics Endpoints

@router.get("/metrics/system")
async def get_system_metrics(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get real-time system metrics."""
    if current_user.role not in [UserRole.ADMIN, UserRole.TEACHER]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions"
        )
    
    # Get recent metrics (last 5 minutes)
    since = datetime.utcnow() - timedelta(minutes=5)
    
    metrics = db.query(RealTimeMetric).filter(
        RealTimeMetric.timestamp >= since,
        RealTimeMetric.entity_type == 'system'
    ).order_by(desc(RealTimeMetric.timestamp)).all()
    
    # Group metrics by name and get latest values
    metric_data = {}
    for metric in metrics:
        if metric.metric_name not in metric_data:
            metric_data[metric.metric_name] = {
                "value": metric.metric_value,
                "type": metric.metric_type,
                "timestamp": metric.timestamp,
                "labels": metric.labels
            }
    
    return metric_data

@router.get("/status")
async def get_websocket_status(
    current_user: User = Depends(get_current_user)
):
    """Get WebSocket connection status and statistics."""
    connected_users = websocket_manager.get_connected_users_count()
    user_online = websocket_manager.is_user_online(current_user.id)
    user_sessions = websocket_manager.get_user_sessions(current_user.id)
    
    return {
        "connected_users": connected_users,
        "user_online": user_online,
        "user_sessions": len(user_sessions),
        "websocket_enabled": True
    }

# Activity Log Endpoints

@router.get("/activity", response_model=List[Dict[str, Any]])
async def get_user_activity(
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    action_filter: Optional[str] = Query(None),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user activity log."""
    query = db.query(ActivityLog).filter(ActivityLog.user_id == current_user.id)
    
    if action_filter:
        query = query.filter(ActivityLog.action.ilike(f"%{action_filter}%"))
    
    total = query.count()
    offset = (page - 1) * per_page
    activities = query.order_by(desc(ActivityLog.created_at)).offset(offset).limit(per_page).all()
    
    activity_list = []
    for activity in activities:
        activity_list.append({
            "id": activity.id,
            "action": activity.action,
            "description": activity.description,
            "entity_type": activity.entity_type,
            "entity_id": activity.entity_id,
            "ip_address": activity.ip_address,
            "success": activity.success,
            "error_message": activity.error_message,
            "created_at": activity.created_at
        })
    
    return activity_list

# Utility functions for sending notifications

async def create_notification(
    user_id: int,
    title: str,
    message: str,
    notification_type: str,
    priority: str = "normal",
    category: Optional[str] = None,
    related_entity_type: Optional[str] = None,
    related_entity_id: Optional[int] = None,
    data: Optional[Dict[str, Any]] = None,
    action_url: Optional[str] = None,
    action_text: Optional[str] = None,
    db: Session = None
):
    """Create a new notification and send it via WebSocket."""
    if not db:
        db = next(get_db())
    
    notification = Notification(
        user_id=user_id,
        title=title,
        message=message,
        notification_type=notification_type,
        priority=priority,
        category=category,
        related_entity_type=related_entity_type,
        related_entity_id=related_entity_id,
        data=data,
        action_url=action_url,
        action_text=action_text
    )
    
    db.add(notification)
    db.commit()
    db.refresh(notification)
    
    # Send real-time notification
    await websocket_manager.send_notification(
        NotificationType(notification_type),
        {
            "id": notification.id,
            "title": title,
            "message": message,
            "priority": priority,
            "category": category,
            "data": data,
            "action_url": action_url,
            "action_text": action_text
        },
        user_id=user_id
    )
    
    return notification

async def log_activity(
    user_id: Optional[int],
    action: str,
    description: Optional[str] = None,
    entity_type: Optional[str] = None,
    entity_id: Optional[int] = None,
    ip_address: Optional[str] = None,
    user_agent: Optional[str] = None,
    session_id: Optional[str] = None,
    context_data: Optional[Dict[str, Any]] = None,
    success: bool = True,
    error_message: Optional[str] = None,
    db: Session = None
):
    """Log user activity."""
    if not db:
        db = next(get_db())
    
    activity = ActivityLog(
        user_id=user_id,
        action=action,
        description=description,
        entity_type=entity_type,
        entity_id=entity_id,
        ip_address=ip_address,
        user_agent=user_agent,
        session_id=session_id,
        context_data=context_data,
        success=success,
        error_message=error_message
    )
    
    db.add(activity)
    db.commit()
    
    return activity
