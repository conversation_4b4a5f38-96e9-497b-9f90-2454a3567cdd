from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, Table, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .base import Base

# Association table for many-to-many relationship between labs and students
lab_students = Table(
    'lab_students',
    Base.metadata,
    Column('lab_id', Integer, Foreign<PERSON>ey('labs.id'), primary_key=True),
    Column('student_id', Integer, ForeignKey('users.id'), primary_key=True)
)

# Association table for many-to-many relationship between labs and VMs
lab_vms = Table(
    'lab_vms',
    Base.metadata,
    Column('lab_id', Integer, ForeignKey('labs.id'), primary_key=True),
    Column('vm_id', Integer, ForeignKey('virtual_machines.id'), primary_key=True)
)

class Lab(Base):
    __tablename__ = "labs"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)
    instructor_id = Column(Integer, Foreign<PERSON>ey("users.id"), nullable=False)
    
    # Lab configuration
    max_students = Column(Integer, default=30)
    max_vms_per_student = Column(Integer, default=3)
    is_active = Column(Boolean, default=True)
    
    # Lab schedule
    start_date = Column(DateTime, nullable=True)
    end_date = Column(DateTime, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    instructor = relationship("User", back_populates="labs_as_instructor")
    students = relationship("User", secondary=lab_students, back_populates="labs_as_student")
    virtual_machines = relationship("VirtualMachine", secondary=lab_vms, back_populates="labs")
    vm_templates = relationship("VMTemplate", secondary="template_labs", back_populates="labs")
    assignments = relationship("LabAssignment", back_populates="lab", cascade="all, delete-orphan")

class LabAssignment(Base):
    __tablename__ = "lab_assignments"

    id = Column(Integer, primary_key=True, index=True)
    lab_id = Column(Integer, ForeignKey("labs.id"), nullable=False)
    student_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    vm_id = Column(Integer, ForeignKey("virtual_machines.id"), nullable=True)
    
    # Assignment details
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    instructions = Column(Text, nullable=True)

    # Assignment configuration
    assignment_type = Column(String(50), default='lab_exercise')  # lab_exercise, project, quiz, practical
    difficulty_level = Column(String(20), default='beginner')  # beginner, intermediate, advanced
    estimated_duration_minutes = Column(Integer, default=60)

    # Required resources
    required_templates = Column(JSON, nullable=True)  # List of template IDs required
    required_vms = Column(Integer, default=1)  # Number of VMs student can create

    # Assignment status
    status = Column(String(20), default='assigned')  # assigned, in_progress, submitted, graded, overdue
    is_active = Column(Boolean, default=True)
    is_completed = Column(Boolean, default=False)
    completion_date = Column(DateTime, nullable=True)

    # Progress tracking
    progress_percentage = Column(Integer, default=0)  # 0-100
    notes = Column(Text, nullable=True)

    # Grading
    max_score = Column(Integer, default=100)
    score = Column(Integer, nullable=True)
    feedback = Column(Text, nullable=True)
    auto_grade = Column(Boolean, default=False)

    # Tracking
    time_spent_minutes = Column(Integer, default=0)
    attempts_count = Column(Integer, default=0)
    last_activity = Column(DateTime, nullable=True)
    submitted_date = Column(DateTime, nullable=True)
    graded_date = Column(DateTime, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    due_date = Column(DateTime, nullable=True)

    # Relationships
    lab = relationship("Lab", back_populates="assignments")
    student = relationship("User", foreign_keys=[student_id])
    virtual_machine = relationship("VirtualMachine", foreign_keys=[vm_id])
    submissions = relationship("AssignmentSubmission", back_populates="assignment", cascade="all, delete-orphan")
    progress_logs = relationship("AssignmentProgressLog", back_populates="assignment", cascade="all, delete-orphan")

class StudentGroup(Base):
    __tablename__ = "student_groups"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)
    instructor_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Group configuration
    max_members = Column(Integer, default=25)
    is_active = Column(Boolean, default=True)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    instructor = relationship("User", back_populates="student_groups")
    members = relationship("StudentGroupMember", back_populates="group", cascade="all, delete-orphan")

class StudentGroupMember(Base):
    __tablename__ = "student_group_members"

    id = Column(Integer, primary_key=True, index=True)
    group_id = Column(Integer, ForeignKey("student_groups.id"), nullable=False)
    student_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Member status
    is_active = Column(Boolean, default=True)
    joined_date = Column(DateTime, default=func.now())
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    group = relationship("StudentGroup", back_populates="members")
    student = relationship("User")

class AssignmentSubmission(Base):
    __tablename__ = "assignment_submissions"

    id = Column(Integer, primary_key=True, index=True)
    assignment_id = Column(Integer, ForeignKey("lab_assignments.id"), nullable=False)
    student_id = Column(Integer, ForeignKey("users.id"), nullable=False)

    # Submission details
    submission_type = Column(String(50), default='file')  # file, text, url, vm_snapshot
    content = Column(Text, nullable=True)  # Text content or file path
    file_path = Column(String(500), nullable=True)  # Path to uploaded file
    file_name = Column(String(255), nullable=True)  # Original file name
    file_size = Column(Integer, nullable=True)  # File size in bytes

    # VM-related submission
    vm_snapshot_id = Column(String(100), nullable=True)  # VM snapshot ID if applicable
    vm_config = Column(JSON, nullable=True)  # VM configuration at submission time

    # Submission metadata
    submission_notes = Column(Text, nullable=True)
    is_final = Column(Boolean, default=False)  # Whether this is the final submission
    version = Column(Integer, default=1)  # Submission version number

    # Grading
    is_graded = Column(Boolean, default=False)
    score = Column(Integer, nullable=True)
    feedback = Column(Text, nullable=True)
    graded_by_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    graded_date = Column(DateTime, nullable=True)

    # Timestamps
    submitted_date = Column(DateTime, default=func.now())
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    assignment = relationship("LabAssignment", back_populates="submissions")
    student = relationship("User", foreign_keys=[student_id])
    graded_by = relationship("User", foreign_keys=[graded_by_id])

class AssignmentProgressLog(Base):
    __tablename__ = "assignment_progress_logs"

    id = Column(Integer, primary_key=True, index=True)
    assignment_id = Column(Integer, ForeignKey("lab_assignments.id"), nullable=False)
    student_id = Column(Integer, ForeignKey("users.id"), nullable=False)

    # Progress details
    action = Column(String(100), nullable=False)  # started, vm_created, step_completed, submitted, etc.
    description = Column(Text, nullable=True)
    progress_percentage = Column(Integer, default=0)

    # Context data
    vm_id = Column(Integer, ForeignKey("virtual_machines.id"), nullable=True)
    step_number = Column(Integer, nullable=True)  # Which step in the assignment
    context_data = Column(JSON, nullable=True)  # Additional context data

    # Time tracking
    session_duration_minutes = Column(Integer, nullable=True)

    # Timestamps
    created_at = Column(DateTime, default=func.now())

    # Relationships
    assignment = relationship("LabAssignment", back_populates="progress_logs")
    student = relationship("User")
    virtual_machine = relationship("VirtualMachine")

class StudentLabAccess(Base):
    __tablename__ = "student_lab_access"

    id = Column(Integer, primary_key=True, index=True)
    student_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    lab_id = Column(Integer, ForeignKey("labs.id"), nullable=False)

    # Access details
    access_granted_date = Column(DateTime, default=func.now())
    access_expires_date = Column(DateTime, nullable=True)
    is_active = Column(Boolean, default=True)

    # Access tracking
    first_access_date = Column(DateTime, nullable=True)
    last_access_date = Column(DateTime, nullable=True)
    total_access_count = Column(Integer, default=0)
    total_time_spent_minutes = Column(Integer, default=0)

    # Permissions
    can_create_vms = Column(Boolean, default=True)
    can_delete_vms = Column(Boolean, default=False)
    max_vms_allowed = Column(Integer, default=3)

    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    student = relationship("User")
    lab = relationship("Lab")
