from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, Table
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .base import Base

# Association table for many-to-many relationship between labs and students
lab_students = Table(
    'lab_students',
    Base.metadata,
    Column('lab_id', Integer, ForeignKey('labs.id'), primary_key=True),
    Column('student_id', Integer, ForeignKey('users.id'), primary_key=True)
)

# Association table for many-to-many relationship between labs and VMs
lab_vms = Table(
    'lab_vms',
    Base.metadata,
    Column('lab_id', Integer, ForeignKey('labs.id'), primary_key=True),
    Column('vm_id', Integer, ForeignKey('virtual_machines.id'), primary_key=True)
)

class Lab(Base):
    __tablename__ = "labs"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)
    instructor_id = Column(Integer, Foreign<PERSON>ey("users.id"), nullable=False)
    
    # Lab configuration
    max_students = Column(Integer, default=30)
    max_vms_per_student = Column(Integer, default=3)
    is_active = Column(Boolean, default=True)
    
    # Lab schedule
    start_date = Column(DateTime, nullable=True)
    end_date = Column(DateTime, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    instructor = relationship("User", back_populates="labs_as_instructor")
    students = relationship("User", secondary=lab_students, back_populates="labs_as_student")
    virtual_machines = relationship("VirtualMachine", secondary=lab_vms, back_populates="labs")
    vm_templates = relationship("VMTemplate", secondary="template_labs", back_populates="labs")
    assignments = relationship("LabAssignment", back_populates="lab", cascade="all, delete-orphan")

class LabAssignment(Base):
    __tablename__ = "lab_assignments"

    id = Column(Integer, primary_key=True, index=True)
    lab_id = Column(Integer, ForeignKey("labs.id"), nullable=False)
    student_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    vm_id = Column(Integer, ForeignKey("virtual_machines.id"), nullable=True)
    
    # Assignment details
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    instructions = Column(Text, nullable=True)
    
    # Assignment status
    is_active = Column(Boolean, default=True)
    is_completed = Column(Boolean, default=False)
    completion_date = Column(DateTime, nullable=True)
    
    # Progress tracking
    progress_percentage = Column(Integer, default=0)  # 0-100
    notes = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    due_date = Column(DateTime, nullable=True)

    # Relationships
    lab = relationship("Lab", back_populates="assignments")
    student = relationship("User", foreign_keys=[student_id])
    virtual_machine = relationship("VirtualMachine", foreign_keys=[vm_id])

class StudentGroup(Base):
    __tablename__ = "student_groups"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)
    instructor_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Group configuration
    max_members = Column(Integer, default=25)
    is_active = Column(Boolean, default=True)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    instructor = relationship("User", back_populates="student_groups")
    members = relationship("StudentGroupMember", back_populates="group", cascade="all, delete-orphan")

class StudentGroupMember(Base):
    __tablename__ = "student_group_members"

    id = Column(Integer, primary_key=True, index=True)
    group_id = Column(Integer, ForeignKey("student_groups.id"), nullable=False)
    student_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Member status
    is_active = Column(Boolean, default=True)
    joined_date = Column(DateTime, default=func.now())
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # Relationships
    group = relationship("StudentGroup", back_populates="members")
    student = relationship("User")
