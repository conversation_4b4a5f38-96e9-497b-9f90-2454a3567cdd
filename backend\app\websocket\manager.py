import socketio
import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import json
from enum import Enum

from ..models.user import User, UserRole
from ..models.virtual_machine import VirtualMachine
from ..models.lab import Lab, LabAssignment
from ..database import get_db

logger = logging.getLogger(__name__)

class NotificationType(str, Enum):
    VM_STATUS_CHANGED = "vm_status_changed"
    ASSIGNMENT_UPDATED = "assignment_updated"
    LAB_UPDATED = "lab_updated"
    SYSTEM_ALERT = "system_alert"
    USER_MESSAGE = "user_message"
    PROGRESS_UPDATED = "progress_updated"
    SUBMISSION_RECEIVED = "submission_received"
    GRADE_ASSIGNED = "grade_assigned"

class WebSocketManager:
    def __init__(self):
        self.sio = socketio.AsyncServer(
            cors_allowed_origins="*",
            logger=True,
            engineio_logger=True
        )
        self.connected_users: Dict[str, Dict[str, Any]] = {}  # session_id -> user_info
        self.user_sessions: Dict[int, List[str]] = {}  # user_id -> [session_ids]
        
        # Register event handlers
        self.setup_event_handlers()
    
    def setup_event_handlers(self):
        """Set up WebSocket event handlers."""
        
        @self.sio.event
        async def connect(sid, environ, auth):
            """Handle client connection."""
            logger.info(f"Client {sid} attempting to connect")
            
            # Extract token from auth
            if not auth or 'token' not in auth:
                logger.warning(f"Client {sid} connection rejected: No token provided")
                await self.sio.disconnect(sid)
                return False
            
            try:
                # Verify token and get user (you'll need to implement token verification)
                user = await self.verify_token(auth['token'])
                if not user:
                    logger.warning(f"Client {sid} connection rejected: Invalid token")
                    await self.sio.disconnect(sid)
                    return False
                
                # Store user session
                self.connected_users[sid] = {
                    'user_id': user.id,
                    'username': user.username,
                    'role': user.role,
                    'connected_at': datetime.utcnow()
                }
                
                # Add to user sessions
                if user.id not in self.user_sessions:
                    self.user_sessions[user.id] = []
                self.user_sessions[user.id].append(sid)
                
                # Join user to their personal room
                await self.sio.enter_room(sid, f"user_{user.id}")
                
                # Join role-based rooms
                await self.sio.enter_room(sid, f"role_{user.role.value}")
                
                # Join lab-specific rooms if student
                if user.role == UserRole.STUDENT:
                    await self.join_student_lab_rooms(sid, user.id)
                elif user.role in [UserRole.TEACHER, UserRole.ADMIN]:
                    await self.join_instructor_lab_rooms(sid, user.id)
                
                logger.info(f"User {user.username} connected with session {sid}")
                
                # Send welcome message
                await self.sio.emit('connected', {
                    'message': 'Connected successfully',
                    'user_id': user.id,
                    'username': user.username,
                    'timestamp': datetime.utcnow().isoformat()
                }, room=sid)
                
                return True
                
            except Exception as e:
                logger.error(f"Error during connection for {sid}: {str(e)}")
                await self.sio.disconnect(sid)
                return False
        
        @self.sio.event
        async def disconnect(sid):
            """Handle client disconnection."""
            if sid in self.connected_users:
                user_info = self.connected_users[sid]
                user_id = user_info['user_id']
                username = user_info['username']
                
                # Remove from user sessions
                if user_id in self.user_sessions:
                    self.user_sessions[user_id].remove(sid)
                    if not self.user_sessions[user_id]:
                        del self.user_sessions[user_id]
                
                # Remove from connected users
                del self.connected_users[sid]
                
                logger.info(f"User {username} disconnected (session {sid})")
        
        @self.sio.event
        async def join_room(sid, data):
            """Handle room join requests."""
            if sid not in self.connected_users:
                return
            
            user_info = self.connected_users[sid]
            room = data.get('room')
            
            if not room:
                return
            
            # Validate room access
            if await self.can_join_room(user_info, room):
                await self.sio.enter_room(sid, room)
                await self.sio.emit('room_joined', {'room': room}, room=sid)
                logger.info(f"User {user_info['username']} joined room {room}")
        
        @self.sio.event
        async def leave_room(sid, data):
            """Handle room leave requests."""
            if sid not in self.connected_users:
                return
            
            room = data.get('room')
            if room:
                await self.sio.leave_room(sid, room)
                await self.sio.emit('room_left', {'room': room}, room=sid)
    
    async def verify_token(self, token: str) -> Optional[User]:
        """Verify JWT token and return user."""
        try:
            # Import here to avoid circular imports
            from ..routers.auth import verify_token
            from sqlalchemy.orm import Session
            
            # Get database session
            db = next(get_db())
            
            # Verify token and get user
            user = await verify_token(token, db)
            return user
            
        except Exception as e:
            logger.error(f"Token verification failed: {str(e)}")
            return None
    
    async def can_join_room(self, user_info: Dict[str, Any], room: str) -> bool:
        """Check if user can join a specific room."""
        user_role = user_info['role']
        user_id = user_info['user_id']
        
        # Personal rooms
        if room == f"user_{user_id}":
            return True
        
        # Role-based rooms
        if room == f"role_{user_role.value}":
            return True
        
        # Lab rooms
        if room.startswith("lab_"):
            lab_id = int(room.split("_")[1])
            return await self.can_access_lab(user_id, user_role, lab_id)
        
        # VM rooms
        if room.startswith("vm_"):
            vm_id = int(room.split("_")[1])
            return await self.can_access_vm(user_id, user_role, vm_id)
        
        # Admin-only rooms
        if room in ["system_alerts", "admin_notifications"]:
            return user_role == UserRole.ADMIN
        
        return False
    
    async def can_access_lab(self, user_id: int, user_role: UserRole, lab_id: int) -> bool:
        """Check if user can access a specific lab."""
        try:
            from sqlalchemy.orm import Session
            db = next(get_db())
            
            lab = db.query(Lab).filter(Lab.id == lab_id).first()
            if not lab:
                return False
            
            # Admins can access all labs
            if user_role == UserRole.ADMIN:
                return True
            
            # Instructors can access their own labs
            if user_role == UserRole.TEACHER and lab.instructor_id == user_id:
                return True
            
            # Students can access labs they're enrolled in
            if user_role == UserRole.STUDENT:
                from ..models.lab import StudentLabAccess
                access = db.query(StudentLabAccess).filter(
                    StudentLabAccess.student_id == user_id,
                    StudentLabAccess.lab_id == lab_id,
                    StudentLabAccess.is_active == True
                ).first()
                return access is not None
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking lab access: {str(e)}")
            return False
    
    async def can_access_vm(self, user_id: int, user_role: UserRole, vm_id: int) -> bool:
        """Check if user can access a specific VM."""
        try:
            from sqlalchemy.orm import Session
            db = next(get_db())
            
            vm = db.query(VirtualMachine).filter(VirtualMachine.id == vm_id).first()
            if not vm:
                return False
            
            # Admins can access all VMs
            if user_role == UserRole.ADMIN:
                return True
            
            # Users can access their own VMs
            if vm.owner_id == user_id:
                return True
            
            # Instructors can access VMs in their labs
            if user_role == UserRole.TEACHER:
                # Check if VM is in instructor's lab
                for lab in vm.labs:
                    if lab.instructor_id == user_id:
                        return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking VM access: {str(e)}")
            return False
    
    async def join_student_lab_rooms(self, sid: str, user_id: int):
        """Join student to their lab rooms."""
        try:
            from sqlalchemy.orm import Session
            from ..models.lab import StudentLabAccess
            
            db = next(get_db())
            
            lab_accesses = db.query(StudentLabAccess).filter(
                StudentLabAccess.student_id == user_id,
                StudentLabAccess.is_active == True
            ).all()
            
            for access in lab_accesses:
                await self.sio.enter_room(sid, f"lab_{access.lab_id}")
                
        except Exception as e:
            logger.error(f"Error joining student lab rooms: {str(e)}")
    
    async def join_instructor_lab_rooms(self, sid: str, user_id: int):
        """Join instructor to their lab rooms."""
        try:
            from sqlalchemy.orm import Session
            
            db = next(get_db())
            
            labs = db.query(Lab).filter(Lab.instructor_id == user_id).all()
            
            for lab in labs:
                await self.sio.enter_room(sid, f"lab_{lab.id}")
                
        except Exception as e:
            logger.error(f"Error joining instructor lab rooms: {str(e)}")
    
    # Notification methods
    async def send_notification(self, notification_type: NotificationType, data: Dict[str, Any], 
                              user_id: Optional[int] = None, room: Optional[str] = None):
        """Send notification to user or room."""
        notification = {
            'type': notification_type.value,
            'data': data,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        if user_id:
            await self.sio.emit('notification', notification, room=f"user_{user_id}")
        elif room:
            await self.sio.emit('notification', notification, room=room)
        else:
            await self.sio.emit('notification', notification)
    
    async def send_vm_status_update(self, vm_id: int, status: str, user_id: Optional[int] = None):
        """Send VM status update."""
        await self.send_notification(
            NotificationType.VM_STATUS_CHANGED,
            {'vm_id': vm_id, 'status': status},
            user_id=user_id
        )
        
        # Also send to VM room
        await self.send_notification(
            NotificationType.VM_STATUS_CHANGED,
            {'vm_id': vm_id, 'status': status},
            room=f"vm_{vm_id}"
        )
    
    async def send_assignment_update(self, assignment_id: int, update_data: Dict[str, Any], 
                                   student_id: Optional[int] = None, lab_id: Optional[int] = None):
        """Send assignment update."""
        notification_data = {'assignment_id': assignment_id, **update_data}
        
        if student_id:
            await self.send_notification(
                NotificationType.ASSIGNMENT_UPDATED,
                notification_data,
                user_id=student_id
            )
        
        if lab_id:
            await self.send_notification(
                NotificationType.ASSIGNMENT_UPDATED,
                notification_data,
                room=f"lab_{lab_id}"
            )
    
    async def send_progress_update(self, assignment_id: int, student_id: int, progress: int):
        """Send progress update."""
        await self.send_notification(
            NotificationType.PROGRESS_UPDATED,
            {
                'assignment_id': assignment_id,
                'student_id': student_id,
                'progress': progress
            },
            user_id=student_id
        )
    
    async def send_system_alert(self, message: str, severity: str = "info"):
        """Send system-wide alert."""
        await self.send_notification(
            NotificationType.SYSTEM_ALERT,
            {'message': message, 'severity': severity},
            room="role_admin"
        )
    
    def get_connected_users_count(self) -> int:
        """Get count of connected users."""
        return len(self.connected_users)
    
    def get_user_sessions(self, user_id: int) -> List[str]:
        """Get all sessions for a user."""
        return self.user_sessions.get(user_id, [])
    
    def is_user_online(self, user_id: int) -> bool:
        """Check if user is online."""
        return user_id in self.user_sessions

# Global WebSocket manager instance
websocket_manager = WebSocketManager()
