"""Add real-time notifications and WebSocket support

Revision ID: af917657d5ed
Revises: f15e2cc5adaf
Create Date: 2025-07-09 09:36:56.335630

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'af917657d5ed'
down_revision = 'f15e2cc5adaf'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('event_streams',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('event_type', sa.String(length=100), nullable=False),
    sa.Column('event_data', sa.JSON(), nullable=False),
    sa.Column('source_type', sa.String(length=50), nullable=False),
    sa.Column('source_id', sa.Integer(), nullable=True),
    sa.Column('target_type', sa.String(length=50), nullable=True),
    sa.Column('target_id', sa.Integer(), nullable=True),
    sa.Column('processed', sa.<PERSON>(), nullable=True),
    sa.Column('processed_at', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    mysql_engine='InnoDB'
    )
    op.create_index(op.f('ix_event_streams_created_at'), 'event_streams', ['created_at'], unique=False)
    op.create_index(op.f('ix_event_streams_event_type'), 'event_streams', ['event_type'], unique=False)
    op.create_index(op.f('ix_event_streams_id'), 'event_streams', ['id'], unique=False)
    op.create_table('realtime_metrics',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('metric_name', sa.String(length=100), nullable=False),
    sa.Column('metric_value', sa.String(length=500), nullable=False),
    sa.Column('metric_type', sa.String(length=50), nullable=False),
    sa.Column('entity_type', sa.String(length=50), nullable=True),
    sa.Column('entity_id', sa.Integer(), nullable=True),
    sa.Column('labels', sa.JSON(), nullable=True),
    sa.Column('timestamp', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_realtime_metrics_id'), 'realtime_metrics', ['id'], unique=False)
    op.create_index(op.f('ix_realtime_metrics_metric_name'), 'realtime_metrics', ['metric_name'], unique=False)
    op.create_index(op.f('ix_realtime_metrics_timestamp'), 'realtime_metrics', ['timestamp'], unique=False)
    op.create_table('activity_logs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('action', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('entity_type', sa.String(length=50), nullable=True),
    sa.Column('entity_id', sa.Integer(), nullable=True),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('user_agent', sa.String(length=500), nullable=True),
    sa.Column('session_id', sa.String(length=100), nullable=True),
    sa.Column('context_data', sa.JSON(), nullable=True),
    sa.Column('success', sa.Boolean(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_activity_logs_id'), 'activity_logs', ['id'], unique=False)
    op.create_table('notification_preferences',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('vm_status_notifications', sa.Boolean(), nullable=True),
    sa.Column('assignment_notifications', sa.Boolean(), nullable=True),
    sa.Column('lab_notifications', sa.Boolean(), nullable=True),
    sa.Column('grade_notifications', sa.Boolean(), nullable=True),
    sa.Column('system_notifications', sa.Boolean(), nullable=True),
    sa.Column('email_notifications', sa.Boolean(), nullable=True),
    sa.Column('push_notifications', sa.Boolean(), nullable=True),
    sa.Column('desktop_notifications', sa.Boolean(), nullable=True),
    sa.Column('digest_frequency', sa.String(length=20), nullable=True),
    sa.Column('quiet_hours_start', sa.String(length=5), nullable=True),
    sa.Column('quiet_hours_end', sa.String(length=5), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_notification_preferences_id'), 'notification_preferences', ['id'], unique=False)
    op.create_table('notifications',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=200), nullable=False),
    sa.Column('message', sa.Text(), nullable=False),
    sa.Column('notification_type', sa.String(length=50), nullable=False),
    sa.Column('priority', sa.String(length=20), nullable=True),
    sa.Column('category', sa.String(length=50), nullable=True),
    sa.Column('is_read', sa.Boolean(), nullable=True),
    sa.Column('is_dismissed', sa.Boolean(), nullable=True),
    sa.Column('related_entity_type', sa.String(length=50), nullable=True),
    sa.Column('related_entity_id', sa.Integer(), nullable=True),
    sa.Column('data', sa.JSON(), nullable=True),
    sa.Column('action_url', sa.String(length=500), nullable=True),
    sa.Column('action_text', sa.String(length=100), nullable=True),
    sa.Column('expires_at', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('read_at', sa.DateTime(), nullable=True),
    sa.Column('dismissed_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_notifications_id'), 'notifications', ['id'], unique=False)
    op.create_table('system_alerts',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=200), nullable=False),
    sa.Column('message', sa.Text(), nullable=False),
    sa.Column('alert_type', sa.String(length=50), nullable=False),
    sa.Column('severity', sa.String(length=20), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('target_roles', sa.JSON(), nullable=True),
    sa.Column('target_users', sa.JSON(), nullable=True),
    sa.Column('is_dismissible', sa.Boolean(), nullable=True),
    sa.Column('show_on_dashboard', sa.Boolean(), nullable=True),
    sa.Column('show_as_banner', sa.Boolean(), nullable=True),
    sa.Column('start_time', sa.DateTime(), nullable=True),
    sa.Column('end_time', sa.DateTime(), nullable=True),
    sa.Column('created_by_id', sa.Integer(), nullable=False),
    sa.Column('data', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['created_by_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_system_alerts_id'), 'system_alerts', ['id'], unique=False)
    op.create_table('websocket_sessions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('session_id', sa.String(length=100), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('user_agent', sa.String(length=500), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('rooms', sa.JSON(), nullable=True),
    sa.Column('connected_at', sa.DateTime(), nullable=True),
    sa.Column('last_activity', sa.DateTime(), nullable=True),
    sa.Column('disconnected_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_websocket_sessions_id'), 'websocket_sessions', ['id'], unique=False)
    op.create_index(op.f('ix_websocket_sessions_session_id'), 'websocket_sessions', ['session_id'], unique=True)
    op.drop_constraint(op.f('virtual_machines_ibfk_1'), 'virtual_machines', type_='foreignkey')
    op.drop_index(op.f('ix_virtual_machines_owner_id'), table_name='virtual_machines')
    op.create_index(op.f('ix_virtual_machines_id'), 'virtual_machines', ['id'], unique=False)
    op.create_foreign_key(None, 'virtual_machines', 'users', ['owner_id'], ['id'])
    op.create_foreign_key(None, 'virtual_machines', 'vm_templates', ['template_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'virtual_machines', type_='foreignkey')
    op.drop_constraint(None, 'virtual_machines', type_='foreignkey')
    op.create_foreign_key(op.f('virtual_machines_ibfk_1'), 'virtual_machines', 'users', ['owner_id'], ['id'], ondelete='CASCADE')
    op.drop_index(op.f('ix_virtual_machines_id'), table_name='virtual_machines')
    op.create_index(op.f('ix_virtual_machines_owner_id'), 'virtual_machines', ['owner_id'], unique=False)
    op.drop_index(op.f('ix_websocket_sessions_session_id'), table_name='websocket_sessions')
    op.drop_index(op.f('ix_websocket_sessions_id'), table_name='websocket_sessions')
    op.drop_table('websocket_sessions')
    op.drop_index(op.f('ix_system_alerts_id'), table_name='system_alerts')
    op.drop_table('system_alerts')
    op.drop_index(op.f('ix_notifications_id'), table_name='notifications')
    op.drop_table('notifications')
    op.drop_index(op.f('ix_notification_preferences_id'), table_name='notification_preferences')
    op.drop_table('notification_preferences')
    op.drop_index(op.f('ix_activity_logs_id'), table_name='activity_logs')
    op.drop_table('activity_logs')
    op.drop_index(op.f('ix_realtime_metrics_timestamp'), table_name='realtime_metrics')
    op.drop_index(op.f('ix_realtime_metrics_metric_name'), table_name='realtime_metrics')
    op.drop_index(op.f('ix_realtime_metrics_id'), table_name='realtime_metrics')
    op.drop_table('realtime_metrics')
    op.drop_index(op.f('ix_event_streams_id'), table_name='event_streams')
    op.drop_index(op.f('ix_event_streams_event_type'), table_name='event_streams')
    op.drop_index(op.f('ix_event_streams_created_at'), table_name='event_streams')
    op.drop_table('event_streams')
    # ### end Alembic commands ###
