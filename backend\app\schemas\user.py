from pydantic import BaseModel, EmailStr, Field
from typing import Optional, List
from datetime import datetime
from ..models.user import UserRole

class UserBase(BaseModel):
    username: str
    email: EmailStr
    full_name: Optional[str] = None

class UserCreate(UserBase):
    password: str

class UserUpdate(BaseModel):
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    password: Optional[str] = None

class ProfileUpdate(BaseModel):
    email: Optional[EmailStr] = None
    fullName: Optional[str] = None
    currentPassword: Optional[str] = None
    newPassword: Optional[str] = None

class UserResponse(UserBase):
    id: int
    role: UserRole
    is_active: bool

    class Config:
        from_attributes = True

# Admin-specific schemas
class AdminUserCreate(BaseModel):
    username: str = Field(..., min_length=3, max_length=50)
    email: EmailStr
    full_name: Optional[str] = Field(None, max_length=100)
    password: str = Field(..., min_length=6)
    role: UserRole = UserRole.STUDENT
    is_active: bool = True

class AdminUserUpdate(BaseModel):
    username: Optional[str] = Field(None, min_length=3, max_length=50)
    email: Optional[EmailStr] = None
    full_name: Optional[str] = Field(None, max_length=100)
    password: Optional[str] = Field(None, min_length=6)
    role: Optional[UserRole] = None
    is_active: Optional[bool] = None

class AdminUserResponse(UserBase):
    id: int
    role: UserRole
    is_active: bool
    created_at: datetime
    updated_at: datetime
    vm_count: Optional[int] = 0

    class Config:
        from_attributes = True

class UserListResponse(BaseModel):
    users: List[AdminUserResponse]
    total: int
    page: int
    per_page: int
    total_pages: int

class UserStatsResponse(BaseModel):
    total_users: int
    active_users: int
    inactive_users: int
    students: int
    teachers: int
    admins: int
    recent_registrations: int  # Last 7 days