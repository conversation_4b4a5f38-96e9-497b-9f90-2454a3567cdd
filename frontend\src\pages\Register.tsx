import React, { useCallback, useState } from 'react';
import { useDispatch } from 'react-redux';
import {
  Box,
  Button,
  Container,
  TextField,
  Typography,
  Paper,
  Grid,
  Alert,
  CircularProgress,
  Link,
} from '@mui/material';
import { useNavigate, Link as RouterLink } from 'react-router-dom';
import { useForm } from '../hooks/useForm';
import { FormConfig } from '../hooks/useForm';
import { useNotification } from '../components/Notifications';
import api from '../services/api';

interface RegisterFormData {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
}

const Register: React.FC = () => {
  const navigate = useNavigate();
  const { showError, showSuccess } = useNotification();
  const [isLoading, setIsLoading] = useState(false);
  const [registerError, setRegisterError] = useState<string | null>(null);

  const { values, errors, touched, handleChange, handleBlur, handleSubmit } =
    useForm<RegisterFormData>({
      username: {
        initialValue: '',
        validation: [{ required: true, message: 'Username is required' }],
      },
      email: {
        initialValue: '',
        validation: [
          { required: true, message: 'Email is required' },
          {
            pattern: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
            message: 'Invalid email address',
          },
        ],
      },
      password: {
        initialValue: '',
        validation: [
          { required: true, message: 'Password is required' },
          {
            custom: (value: string) => value.length >= 8,
            message: 'Password must be at least 8 characters',
          },
        ],
      },
      confirmPassword: {
        initialValue: '',
        validation: [
          { required: true, message: 'Please confirm your password' },
          {
            custom: (value: string, values: RegisterFormData) =>
              value === values.password,
            message: 'Passwords do not match',
          },
        ],
      },
    });

  const onSubmit = useCallback(async () => {
    try {
      setIsLoading(true);
      setRegisterError(null);

      await api.register({
        username: values.username,
        email: values.email,
        password: values.password,
      });

      showSuccess(
        'Registration successful! Please sign in with your new account.'
      );
      navigate('/login');
    } catch (error: any) {
      console.error('Registration failed:', error);
      let errorMessage = 'Registration failed. Please try again.';

      if (error?.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      setRegisterError(errorMessage);
      showError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [values, navigate, showError, showSuccess]);

  return (
    <Container component="main" maxWidth="xs">
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Paper elevation={3} sx={{ p: 4, width: '100%' }}>
          <Typography
            component="h1"
            variant="h4"
            align="center"
            gutterBottom
            color="primary"
          >
            Proxmox Lab Manager
          </Typography>
          <Typography
            component="h2"
            variant="h6"
            align="center"
            gutterBottom
            sx={{ mb: 3 }}
          >
            Create Your Account
          </Typography>

          {registerError && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {registerError}
            </Alert>
          )}

          <form onSubmit={handleSubmit(onSubmit)} noValidate>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  id="username"
                  name="username"
                  label="Username"
                  value={values.username}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.username && !!errors.username}
                  helperText={
                    (touched.username && errors.username) ||
                    'Choose a unique username (3-20 characters)'
                  }
                  disabled={isLoading}
                  autoComplete="username"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  id="email"
                  name="email"
                  label="Email Address"
                  type="email"
                  value={values.email}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.email && !!errors.email}
                  helperText={
                    (touched.email && errors.email) ||
                    "We'll use this for account recovery"
                  }
                  disabled={isLoading}
                  autoComplete="email"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  id="password"
                  name="password"
                  label="Password"
                  type="password"
                  value={values.password}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.password && !!errors.password}
                  helperText={
                    (touched.password && errors.password) ||
                    'Minimum 8 characters with letters and numbers'
                  }
                  disabled={isLoading}
                  autoComplete="new-password"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  id="confirmPassword"
                  name="confirmPassword"
                  label="Confirm Password"
                  type="password"
                  value={values.confirmPassword}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.confirmPassword && !!errors.confirmPassword}
                  helperText={
                    (touched.confirmPassword && errors.confirmPassword) ||
                    'Re-enter your password to confirm'
                  }
                  disabled={isLoading}
                  autoComplete="new-password"
                />
              </Grid>
              <Grid item xs={12}>
                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  color="primary"
                  disabled={isLoading}
                  sx={{ mt: 2, mb: 2, py: 1.5 }}
                  startIcon={
                    isLoading ? (
                      <CircularProgress size={20} color="inherit" />
                    ) : null
                  }
                >
                  {isLoading ? 'Creating Account...' : 'Create Account'}
                </Button>
              </Grid>
              <Grid item xs={12}>
                <Box sx={{ textAlign: 'center', mt: 2 }}>
                  <Typography variant="body2">
                    Already have an account?{' '}
                    <Link component={RouterLink} to="/login" color="primary">
                      Sign in here
                    </Link>
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </form>
        </Paper>
      </Box>
    </Container>
  );
};

export default Register;
