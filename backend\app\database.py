from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.ext.declarative import declarative_base
from typing import Generator
import os
from dotenv import load_dotenv

load_dotenv()

SQLALCHEMY_DATABASE_URL = os.getenv(
    "DATABASE_URL",
    "sqlite:///./lab_management.db"
)

# Configure engine based on database type
if SQLALCHEMY_DATABASE_URL.startswith("sqlite"):
    engine = create_engine(
        SQLALCHEMY_DATABASE_URL,
        connect_args={"check_same_thread": False}
    )
else:
    engine = create_engine(
        SQLALCHEMY_DATABASE_URL,
        pool_pre_ping=True
    )

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()
def get_db() -> Generator[Session, None, None]:
    """Get a database session."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def init_db():
    """Initialize the database."""
    # Import all models to ensure they are registered with Base
    from .models import (
        User, VirtualMachine, Lab, LabAssignment, StudentGroup,
        StudentGroupMember, AssignmentSubmission, AssignmentProgressLog,
        StudentLabAccess, VMTemplate, TemplateCategory, TemplateCategoryAssignment,
        TemplateVersion, TemplateUsageLog, Notification, SystemAlert,
        NotificationPreference, ActivityLog, WebSocketSession, RealTimeMetric, EventStream
    )
    Base.metadata.create_all(bind=engine)