import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Container,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Pagination,
  Tooltip,
  Alert,
  CircularProgress,
  useTheme,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Group as GroupIcon,
  Computer as ComputerIcon,
  Assignment as AssignmentIcon,
  Refresh as RefreshIcon,
  School as SchoolIcon,
  People as PeopleIcon,
  PlayArrow as PlayArrowIcon,
  Stop as StopIcon,
} from '@mui/icons-material';
import { useSelector } from 'react-redux';
import { RootState } from '../../types/store';
import { UserRole } from '../../types';
import api from '../../services/api';
import CreateLabDialog from '../../components/admin/CreateLabDialog';
import EditLabDialog from '../../components/admin/EditLabDialog';

interface Lab {
  id: number;
  name: string;
  description: string | null;
  instructor_id: number;
  instructor_name: string | null;
  max_students: number;
  max_vms_per_student: number;
  is_active: boolean;
  start_date: string | null;
  end_date: string | null;
  student_count: number;
  vm_count: number;
  assignment_count: number;
  created_at: string;
  updated_at: string;
}

interface LabStats {
  total_labs: number;
  active_labs: number;
  inactive_labs: number;
  total_assignments: number;
  completed_assignments: number;
  pending_assignments: number;
  total_students_enrolled: number;
  total_groups: number;
}

const LabManagement: React.FC = () => {
  const theme = useTheme();
  const { user } = useSelector((state: RootState) => state.auth);

  // State
  const [labs, setLabs] = useState<Lab[]>([]);
  const [stats, setStats] = useState<LabStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Filters and pagination
  const [search, setSearch] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);
  const perPage = 10;

  // Dialog states
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedLab, setSelectedLab] = useState<Lab | null>(null);

  // Check if current user can manage labs
  const canManageLabs =
    (user?.role as UserRole) === UserRole.ADMIN || (user?.role as UserRole) === UserRole.TEACHER;

  useEffect(() => {
    if (!canManageLabs) {
      setError('Instructor or admin access required');
      setLoading(false);
      return;
    }

    fetchLabs();
    fetchStats();
  }, [canManageLabs, page, search, statusFilter]);

  const fetchLabs = async () => {
    try {
      setLoading(true);
      const params: any = {
        page,
        per_page: perPage,
      };

      if (search) params.search = search;
      if (statusFilter !== '') params.is_active = statusFilter === 'true';

      const data = await api.getLabs(params);
      setLabs(data.labs);
      setTotal(data.total);
      setTotalPages(data.total_pages);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch labs');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const data = await api.getLabStats();
      setStats(data);
    } catch (err) {
      console.error('Failed to fetch lab stats:', err);
    }
  };

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(event.target.value);
    setPage(1); // Reset to first page when searching
  };

  const handleStatusFilter = (event: any) => {
    setStatusFilter(event.target.value);
    setPage(1);
  };

  const handlePageChange = (
    event: React.ChangeEvent<unknown>,
    value: number
  ) => {
    setPage(value);
  };

  const handleCreateLab = () => {
    setCreateDialogOpen(true);
  };

  const handleEditLab = (lab: Lab) => {
    setSelectedLab(lab);
    setEditDialogOpen(true);
  };

  const handleLabCreated = () => {
    fetchLabs();
    fetchStats();
  };

  const handleLabUpdated = () => {
    fetchLabs();
    fetchStats();
  };

  const handleDeleteLab = async (lab: Lab) => {
    if (
      window.confirm(
        `Are you sure you want to delete lab "${lab.name}"? This action cannot be undone.`
      )
    ) {
      try {
        await api.deleteLab(lab.id);
        fetchLabs(); // Refresh the list
        fetchStats(); // Refresh stats
      } catch (error) {
        setError(
          error instanceof Error ? error.message : 'Failed to delete lab'
        );
      }
    }
  };

  const handleToggleLabStatus = async (lab: Lab) => {
    try {
      await api.updateLab(lab.id, { is_active: !lab.is_active });
      fetchLabs(); // Refresh the list
    } catch (error) {
      setError(
        error instanceof Error ? error.message : 'Failed to update lab status'
      );
    }
  };

  if (!canManageLabs) {
    return (
      <Container maxWidth="lg">
        <Alert severity="error" sx={{ mt: 4 }}>
          Instructor or admin access required to view this page.
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl">
      <Box sx={{ py: 4 }}>
        {/* Header */}
        <Box
          sx={{
            mb: 4,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Typography variant="h4" fontWeight={700}>
            Lab Management
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateLab}
          >
            Create Lab
          </Button>
        </Box>

        {/* Stats Cards */}
        {stats && (
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <SchoolIcon color="primary" sx={{ fontSize: 40 }} />
                    <Box>
                      <Typography variant="h4" fontWeight={700}>
                        {stats.total_labs}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Total Labs
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <PlayArrowIcon color="success" sx={{ fontSize: 40 }} />
                    <Box>
                      <Typography variant="h4" fontWeight={700}>
                        {stats.active_labs}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Active Labs
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <AssignmentIcon color="warning" sx={{ fontSize: 40 }} />
                    <Box>
                      <Typography variant="h4" fontWeight={700}>
                        {stats.total_assignments}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Assignments
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <PeopleIcon color="info" sx={{ fontSize: 40 }} />
                    <Box>
                      <Typography variant="h4" fontWeight={700}>
                        {stats.total_students_enrolled}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        Students Enrolled
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}

        {/* Filters */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  placeholder="Search labs..."
                  value={search}
                  onChange={handleSearch}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={statusFilter}
                    onChange={handleStatusFilter}
                    label="Status"
                  >
                    <MenuItem value="">All Status</MenuItem>
                    <MenuItem value="true">Active</MenuItem>
                    <MenuItem value="false">Inactive</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={2}>
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  onClick={() => {
                    fetchLabs();
                    fetchStats();
                  }}
                >
                  Refresh
                </Button>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Error Alert */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Labs Table */}
        <Card>
          <CardContent>
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                <CircularProgress />
              </Box>
            ) : (
              <>
                <TableContainer component={Paper} elevation={0}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Lab Details</TableCell>
                        <TableCell>Instructor</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Students</TableCell>
                        <TableCell>VMs</TableCell>
                        <TableCell>Assignments</TableCell>
                        <TableCell>Created</TableCell>
                        <TableCell align="right">Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {labs.map((lab) => (
                        <TableRow key={lab.id}>
                          <TableCell>
                            <Box>
                              <Typography variant="subtitle2" fontWeight={600}>
                                {lab.name}
                              </Typography>
                              {lab.description && (
                                <Typography
                                  variant="body2"
                                  color="textSecondary"
                                >
                                  {lab.description}
                                </Typography>
                              )}
                              <Typography
                                variant="caption"
                                color="textSecondary"
                              >
                                Max: {lab.max_students} students,{' '}
                                {lab.max_vms_per_student} VMs/student
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {lab.instructor_name || 'Unknown'}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={lab.is_active ? 'Active' : 'Inactive'}
                              color={lab.is_active ? 'success' : 'default'}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: 1,
                              }}
                            >
                              <GroupIcon fontSize="small" />
                              <Typography variant="body2">
                                {lab.student_count}/{lab.max_students}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: 1,
                              }}
                            >
                              <ComputerIcon fontSize="small" />
                              <Typography variant="body2">
                                {lab.vm_count}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: 1,
                              }}
                            >
                              <AssignmentIcon fontSize="small" />
                              <Typography variant="body2">
                                {lab.assignment_count}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {new Date(lab.created_at).toLocaleDateString()}
                            </Typography>
                          </TableCell>
                          <TableCell align="right">
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <Tooltip title="Edit Lab">
                                <IconButton
                                  size="small"
                                  onClick={() => handleEditLab(lab)}
                                >
                                  <EditIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip
                                title={
                                  lab.is_active ? 'Deactivate' : 'Activate'
                                }
                              >
                                <IconButton
                                  size="small"
                                  onClick={() => handleToggleLabStatus(lab)}
                                  color={lab.is_active ? 'warning' : 'success'}
                                >
                                  {lab.is_active ? (
                                    <StopIcon />
                                  ) : (
                                    <PlayArrowIcon />
                                  )}
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Delete Lab">
                                <IconButton
                                  size="small"
                                  onClick={() => handleDeleteLab(lab)}
                                  color="error"
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </Tooltip>
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>

                {/* Pagination */}
                {totalPages > 1 && (
                  <Box
                    sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}
                  >
                    <Pagination
                      count={totalPages}
                      page={page}
                      onChange={handlePageChange}
                      color="primary"
                    />
                  </Box>
                )}

                {labs.length === 0 && !loading && (
                  <Box sx={{ textAlign: 'center', py: 4 }}>
                    <Typography variant="h6" color="textSecondary">
                      No labs found
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Create your first lab to get started
                    </Typography>
                  </Box>
                )}
              </>
            )}
          </CardContent>
        </Card>

        {/* Dialogs */}
        <CreateLabDialog
          open={createDialogOpen}
          onClose={() => setCreateDialogOpen(false)}
          onLabCreated={handleLabCreated}
          currentUserRole={(user?.role as UserRole) || UserRole.STUDENT}
        />

        <EditLabDialog
          open={editDialogOpen}
          lab={selectedLab}
          onClose={() => {
            setEditDialogOpen(false);
            setSelectedLab(null);
          }}
          onLabUpdated={handleLabUpdated}
        />
      </Box>
    </Container>
  );
};

export default LabManagement;
