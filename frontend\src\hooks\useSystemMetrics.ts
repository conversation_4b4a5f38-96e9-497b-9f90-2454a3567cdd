import { useState, useEffect, useCallback } from 'react';
import api from '../services/api';

interface SystemMetrics {
  timestamp: string;
  cluster: {
    total_nodes: number;
    online_nodes: number;
    health: string;
  };
  resources: {
    cpu: {
      usage_percentage: number;
      status: string;
    };
    memory: {
      usage_percentage: number;
      status: string;
    };
    disk: {
      usage_percentage: number;
      status: string;
    };
  };
  virtual_machines: {
    total_vms: number;
    running_vms: number;
    stopped_vms: number;
    suspended_vms: number;
    vm_types: {
      kvm: number;
      lxc: number;
    };
    resource_allocation: {
      total_cpu_cores: number;
      total_memory_mb: number;
      total_disk_gb: number;
    };
  };
  nodes: any[];
}

interface HistoricalMetrics {
  period_hours: number;
  timestamps: string[];
  metrics: {
    cpu_usage: number[];
    memory_usage: number[];
    disk_usage: number[];
  };
}

interface UseSystemMetricsOptions {
  refreshInterval?: number;
  autoRefresh?: boolean;
}

export const useSystemMetrics = (options: UseSystemMetricsOptions = {}) => {
  const { refreshInterval = 30000, autoRefresh = true } = options;

  const [metrics, setMetrics] = useState<SystemMetrics | null>(null);
  const [historicalMetrics, setHistoricalMetrics] =
    useState<HistoricalMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchMetrics = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const [currentMetrics, historical] = await Promise.all([
        api.getSystemMetrics(),
        api.getHistoricalMetrics(24),
      ]);

      setMetrics(currentMetrics);
      setHistoricalMetrics(historical);
    } catch (err) {
      const message =
        err instanceof Error ? err.message : 'Failed to fetch system metrics';
      setError(message);
      console.error('Error fetching system metrics:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshMetrics = useCallback(() => {
    fetchMetrics();
  }, [fetchMetrics]);

  useEffect(() => {
    // Initial fetch
    fetchMetrics();

    // Set up auto-refresh if enabled
    let intervalId: NodeJS.Timeout | null = null;
    if (autoRefresh && refreshInterval > 0) {
      intervalId = setInterval(fetchMetrics, refreshInterval);
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [fetchMetrics, autoRefresh, refreshInterval]);

  return {
    metrics,
    historicalMetrics,
    isLoading,
    error,
    refreshMetrics,
  };
};
